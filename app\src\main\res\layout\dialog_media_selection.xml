<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:orientation="vertical"
    android:padding="16dp">

    <!-- 前置摄像头 -->
    <LinearLayout
        android:id="@+id/btn_front_camera"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="horizontal"
        android:padding="16dp"
        android:background="?android:attr/selectableItemBackground"
        android:clickable="true"
        android:focusable="true">

        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="📷"
            android:textSize="24sp"
            android:layout_marginEnd="16dp" />

        <TextView
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:text="前置摄像头"
            android:textSize="16sp"
            android:textColor="?android:attr/textColorPrimary" />

    </LinearLayout>

    <!-- 后置摄像头 -->
    <LinearLayout
        android:id="@+id/btn_rear_camera"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="horizontal"
        android:padding="16dp"
        android:background="?android:attr/selectableItemBackground"
        android:clickable="true"
        android:focusable="true">

        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="📹"
            android:textSize="24sp"
            android:layout_marginEnd="16dp" />

        <TextView
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:text="后置摄像头"
            android:textSize="16sp"
            android:textColor="?android:attr/textColorPrimary" />

    </LinearLayout>

    <!-- 视频 -->
    <LinearLayout
        android:id="@+id/btn_video"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="horizontal"
        android:padding="16dp"
        android:background="?android:attr/selectableItemBackground"
        android:clickable="true"
        android:focusable="true">

        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="🎥"
            android:textSize="24sp"
            android:layout_marginEnd="16dp" />

        <TextView
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:text="视频"
            android:textSize="16sp"
            android:textColor="?android:attr/textColorPrimary" />

    </LinearLayout>

    <!-- 图片 -->
    <LinearLayout
        android:id="@+id/btn_picture"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="horizontal"
        android:padding="16dp"
        android:background="?android:attr/selectableItemBackground"
        android:clickable="true"
        android:focusable="true">

        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="🖼️"
            android:textSize="24sp"
            android:layout_marginEnd="16dp" />

        <TextView
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:text="图片"
            android:textSize="16sp"
            android:textColor="?android:attr/textColorPrimary" />

    </LinearLayout>

    <!-- 文本 -->
    <LinearLayout
        android:id="@+id/btn_text"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="horizontal"
        android:padding="16dp"
        android:background="?android:attr/selectableItemBackground"
        android:clickable="true"
        android:focusable="true">

        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="📝"
            android:textSize="24sp"
            android:layout_marginEnd="16dp" />

        <TextView
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:text="文本"
            android:textSize="16sp"
            android:textColor="?android:attr/textColorPrimary" />

    </LinearLayout>

</LinearLayout>
