package com.example.castapp.ui.windowsettings

import android.content.Context
import android.graphics.Canvas
import android.graphics.Color
import android.graphics.Matrix
import android.graphics.Outline
import android.graphics.Paint
import android.graphics.Rect
import android.graphics.RectF
import android.util.TypedValue
import android.view.TextureView
import android.view.View
import android.view.ViewGroup
import android.view.ViewOutlineProvider
import android.view.ViewTreeObserver
import android.widget.FrameLayout
import com.example.castapp.utils.AppLog

/**
 * 变换渲染器
 * 负责Matrix变换的具体应用和UI效果渲染
 */
class TransformRenderer(
    private val context: Context,
    private val container: FrameLayout,
    private val windowWidth: Int,
    private val windowHeight: Int
) {

    // Matrix变换核心
    private val transformMatrix = Matrix()

    // 圆角半径（dp）
    private var cornerRadius = 16f

    // 透明度（0.0f-1.0f）
    private var windowAlpha = 1.0f

    // 边框显示状态（默认关闭）
    private var isBorderEnabled = false

    // 边框颜色（默认灰色）
    private var borderColor = 0xFFFFEAA7.toInt()

    // 边框宽度（dp，默认2dp）
    private var borderWidth = 2f

    // 🎯 统一边框法：边框视图
    private var cropBorderView: View? = null

    // 🎯 统一边框法：当前是否已创建边框视图
    private var isCropBorderExpanded = false

    // 🎯 预分配对象：避免在onDraw中创建对象
    private val borderPaint = Paint().apply {
        style = Paint.Style.STROKE
        isAntiAlias = true
    }
    private val borderRect = RectF()

    // 🎯 裁剪模式边框管理：保存裁剪模式前的边框状态
    private var borderStateBeforeCrop: Boolean = false

    // 🎯 根本性修复：引入位置管理器
    private val positionManager = WindowPositionManager(container, windowWidth, windowHeight)

    // 🎯 横竖屏适配：当前视频方向和分辨率
    private var currentVideoOrientation = android.content.res.Configuration.ORIENTATION_PORTRAIT
    private var currentVideoWidth = 0
    private var currentVideoHeight = 0
    
    init {
        // 应用默认圆角
        applyCornerRadius()
    }
    
    /**
     * 应用所有变换（简化版：移除裁剪处理，由统一裁剪管理器处理）
     */
    fun applyTransforms(
        textureView: TextureView?,
        targetView: View? = null,
        connectionId: String,
        isCroppedWindow: Boolean,
        cropRectRatio: RectF?,
        isMirrored: Boolean,

        currentScaleFactor: Float,
        currentRotation: Float,
        currentPivotX: Float,
        currentPivotY: Float
    ) {
        AppLog.d("🎯 [简化变换] 开始应用变换: 连接=$connectionId")


        // 🎯 简化：移除裁剪处理，只应用容器变换（缩放、旋转）
        applyContainerTransforms(
            isCroppedWindow, cropRectRatio,
            currentScaleFactor, currentRotation,
            currentPivotX, currentPivotY
        )

        // 🎯 统一变换：所有窗口类型都使用TextureView Matrix变换
        val actualTextureView = textureView ?: (targetView as? TextureView)
        if (actualTextureView != null) {
            // 应用 TextureView Matrix 变换（不包含裁剪）
            applyTextureViewTransforms(actualTextureView, isMirrored, isCroppedWindow, cropRectRatio)
        } else if (targetView != null) {
            // 图片媒体窗口仍使用View变换（ImageView不支持Matrix）
            applyImageViewTransforms(targetView, isMirrored)
        }

        AppLog.d("🎯 [简化变换] 变换应用完成: 容器位置=(${container.x}, ${container.y})")
    }
    


    /**
     * 应用容器变换（简化版：移除复杂偏移计算）
     */
    private fun applyContainerTransforms(
        isCroppedWindow: Boolean,
        cropRectRatio: RectF?,
        currentScaleFactor: Float,
        currentRotation: Float,
        currentPivotX: Float,
        currentPivotY: Float
    ) {
        AppLog.d("🎯 applyContainerTransforms开始: 容器位置=(${container.x}, ${container.y})")

        // 🎯 简化：移除复杂的偏移补偿计算，容器位置由container.x/y直接控制
        // 注意：isCroppedWindow, cropRectRatio, currentPivotX, currentPivotY 参数保留用于接口兼容性，但现在不再使用

        // 🎯 修改：所有窗口都使用左上角作为变换中心点
        val adjustedPivotX = 0f
        val adjustedPivotY = 0f

        // 🎯 修复：保持translationX/Y不变，位置由translation控制
        // 不再强制清零translationX/Y，保持当前值
        AppLog.d("🎯 保持translation: (${container.translationX}, ${container.translationY})")

        // 应用缩放
        container.scaleX = currentScaleFactor
        container.scaleY = currentScaleFactor
        AppLog.d("🎯 应用缩放后: 容器位置=(${container.x}, ${container.y})")

        // 应用旋转（仅用户手动旋转，横屏旋转在TextureView层处理）
        container.rotation = currentRotation
        AppLog.d("🎯 应用旋转后: 容器位置=(${container.x}, ${container.y})")

        // 设置变换中心点
        container.pivotX = adjustedPivotX
        container.pivotY = adjustedPivotY
        AppLog.d("🎯 设置pivot后: 容器位置=(${container.x}, ${container.y})")

        AppLog.v("🎯 容器变换已应用（简化版）: 绝对位置=(${container.x}, ${container.y}), 缩放=${currentScaleFactor}, 旋转=${currentRotation}°")
        AppLog.v("🎯 变换中心点: (${adjustedPivotX}, ${adjustedPivotY})")
    }

    /**
     * 应用 TextureView Matrix 变换（裁剪适配版：支持裁剪窗口的镜像变换）
     * 🎯 关键修复：完全移除裁剪相关的Matrix变换，避免与clipBounds冲突
     */
    private fun applyTextureViewTransforms(
        textureView: TextureView?,
        isMirrored: Boolean,
        isCroppedWindow: Boolean,
        cropRectRatio: RectF?
    ) {
        val texture = textureView ?: return

        // 🎯 修复：保存容器translation位置，与setPrecisionTransform()保持一致
        val savedTranslationX = container.translationX
        val savedTranslationY = container.translationY

        AppLog.d("🎯 [简化TextureView变换] 开始: 保存translation位置=(${savedTranslationX}, ${savedTranslationY})")

        // 重置 Matrix
        transformMatrix.reset()

        // 🎯 横竖屏适配：检查是否需要变换（镜像或方向适配）
        val needsOrientationTransform = currentVideoOrientation == android.content.res.Configuration.ORIENTATION_LANDSCAPE
        val needsTransform = isMirrored || needsOrientationTransform

        AppLog.d("🎯 [横竖屏适配TextureView变换] needsTransform=$needsTransform (镜像=$isMirrored, 方向适配=$needsOrientationTransform, 当前方向=${getOrientationName(currentVideoOrientation)})")

        if (needsTransform) {
            // 设置 TextureView 为 MATCH_PARENT，以便 Matrix 变换生效
            texture.layoutParams = FrameLayout.LayoutParams(
                FrameLayout.LayoutParams.MATCH_PARENT,
                FrameLayout.LayoutParams.MATCH_PARENT
            )
            // 🎯 强制刷新TextureView布局
            texture.requestLayout()

            // 🎯 集中化：通过位置管理器恢复容器位置
            positionManager.setContainerPosition(savedTranslationX, savedTranslationY)

            // 🎯 横竖屏适配：应用方向变换
            if (needsOrientationTransform) {
                applyOrientationTransform()
            }

            // 应用镜像变换（如果启用）
            if (isMirrored) {
                applyMirrorTransform(isCroppedWindow, cropRectRatio)
            }

            texture.setTransform(transformMatrix)
            AppLog.v("🎯 TextureView 变换已应用: 镜像=$isMirrored, 方向适配=$needsOrientationTransform")
        } else {
            AppLog.d("🎯 进入else分支，清除TextureView变换")
            AppLog.d("🎯 requestLayout前容器translation: (${container.translationX}, ${container.translationY})")

            // 清除 Matrix 变换，恢复 MATCH_PARENT
            texture.setTransform(null)
            texture.layoutParams = FrameLayout.LayoutParams(
                FrameLayout.LayoutParams.MATCH_PARENT,
                FrameLayout.LayoutParams.MATCH_PARENT
            )
            // 🎯 强制刷新TextureView布局
            texture.requestLayout()

            AppLog.d("🎯 requestLayout后容器translation: (${container.translationX}, ${container.translationY})")

            // 🎯 集中化：通过位置管理器直接设置位置
            // 避免异步恢复覆盖其他逻辑设置的正确位置
            positionManager.setContainerPosition(savedTranslationX, savedTranslationY)
            AppLog.d("🎯 直接恢复translation位置: (${savedTranslationX}, ${savedTranslationY})")
            AppLog.v("🎯 TextureView Matrix 已清除，恢复 MATCH_PARENT")
        }
    }

    /**
     * 应用图片视图变换（ImageView专用，简化版：移除裁剪处理）
     * 🎯 关键修复：ImageView也移除裁剪处理，统一由clipBounds处理
     */
    private fun applyImageViewTransforms(
        imageView: View,
        isMirrored: Boolean
    ) {
        AppLog.d("🎯 [简化ImageView变换] 开始: 类型=${imageView.javaClass.simpleName}")

        // 保存当前容器位置
        val savedTranslationX = container.translationX
        val savedTranslationY = container.translationY

        // 🎯 简化：只检查镜像
        val needsTransform = isMirrored

        AppLog.d("🎯 [简化ImageView变换] needsTransform=$needsTransform (镜像=$isMirrored)")

        if (needsTransform) {
            // 恢复容器位置
            positionManager.setContainerPosition(savedTranslationX, savedTranslationY)

            // 应用变换到图片视图（在needsTransform=true的分支中，isMirrored必然为true）
            val scaleX = -1f  // 镜像变换
            val scaleY = 1f
            val rotation = 0f
            val translationX = 0f
            val translationY = 0f

            // 设置为MATCH_PARENT，让clipBounds发挥作用
            imageView.layoutParams = FrameLayout.LayoutParams(
                FrameLayout.LayoutParams.MATCH_PARENT,
                FrameLayout.LayoutParams.MATCH_PARENT
            )
            imageView.requestLayout()

            // 应用所有变换
            imageView.scaleX = scaleX
            imageView.scaleY = scaleY
            imageView.rotation = rotation
            imageView.translationX = translationX
            imageView.translationY = translationY

            AppLog.v("🎯 图片视图变换已应用: 缩放=(${scaleX}, ${scaleY}), 旋转=${rotation}°, 偏移=(${translationX}, ${translationY})")
        } else {
            // 清除所有变换
            imageView.scaleX = 1f
            imageView.scaleY = 1f
            imageView.rotation = 0f
            imageView.translationX = 0f
            imageView.translationY = 0f

            // 恢复容器位置
            positionManager.setContainerPosition(savedTranslationX, savedTranslationY)
            AppLog.v("🎯 图片视图变换已清除")
        }
    }





    /**
     * 应用镜像变换（裁剪适配版：根据裁剪状态使用正确的镜像轴心点）
     */
    private fun applyMirrorTransform(isCroppedWindow: Boolean, cropRectRatio: RectF?) {
        // 🎯 裁剪适配：使用传入的裁剪状态参数

        val centerX = if (isCroppedWindow && cropRectRatio != null) {
            // 🎯 裁剪窗口：使用裁剪区域的中心点作为镜像轴
            val cropLeft = cropRectRatio.left * windowWidth
            val cropRight = cropRectRatio.right * windowWidth
            val cropCenterX = (cropLeft + cropRight) / 2f

            // 🎯 关键：镜像轴心点需要相对于容器原点的偏移
            cropCenterX
        } else {
            // 🎯 普通窗口：使用窗口中心作为镜像轴
            windowWidth / 2f
        }

        // 应用水平镜像变换：先移动到原点，水平翻转，再移回中心
        transformMatrix.postTranslate(-centerX, 0f)  // 移动到原点
        transformMatrix.postScale(-1f, 1f)           // 水平镜像
        transformMatrix.postTranslate(centerX, 0f)   // 移回中心

        if (isCroppedWindow && cropRectRatio != null) {
            AppLog.v("🎯 裁剪窗口镜像变换已应用: 镜像轴中心X=${centerX}")
            AppLog.v("🎯   裁剪区域: left=${cropRectRatio.left * windowWidth}, right=${cropRectRatio.right * windowWidth}")
        } else {
            AppLog.v("🎯 普通窗口镜像变换已应用: 镜像轴中心X=${centerX}")
        }
    }



    /**
     * 设置圆角半径
     */
    fun setCornerRadius(radius: Float) {
        cornerRadius = radius
        applyCornerRadius()
        AppLog.d("🎯 圆角半径设置为: ${radius}dp")
    }

    /**
     * 获取当前圆角半径
     */
    fun getCornerRadius(): Float = cornerRadius

    /**
     * 🎯 新增：为裁剪状态应用圆角和边框（公共方法）
     */
    fun applyCornerRadiusForCrop() {
        AppLog.d("🎯 [边框恢复] applyCornerRadiusForCrop被调用，当前边框状态: $isBorderEnabled")
        applyCornerRadius()
    }

    /**
     * 应用圆角效果（统一版：所有窗口都使用父容器边框法）
     * 🎯 统一边框法：移除复杂的分支逻辑，统一使用父容器边框法
     */
    private fun applyCornerRadius() {
        val radiusPx = dpToPxFloat(cornerRadius)

        // 🎯 统一边框法：获取当前的显示区域（考虑裁剪状态）
        val clipBounds = container.clipBounds
        val displayWidth = clipBounds?.width() ?: container.width
        val displayHeight = clipBounds?.height() ?: container.height

        AppLog.d("🎯 [统一边框法] applyCornerRadius被调用")
        AppLog.d("🎯 [统一边框法] 当前边框状态: isBorderEnabled=$isBorderEnabled")
        AppLog.d("🎯 [统一边框法] 显示区域: ${displayWidth}x${displayHeight}, 裁剪状态: ${clipBounds != null}")

        // 🎥 关键修复：如果容器尺寸为0，延迟边框创建
        if (displayWidth <= 0 || displayHeight <= 0) {
            AppLog.d("🎯 [统一边框法] 容器尺寸为0，延迟边框创建")
            // 设置布局监听器，等待容器有正确尺寸后再创建边框
            container.viewTreeObserver.addOnGlobalLayoutListener(object : ViewTreeObserver.OnGlobalLayoutListener {
                override fun onGlobalLayout() {
                    container.viewTreeObserver.removeOnGlobalLayoutListener(this)
                    AppLog.d("🎯 [统一边框法] 容器布局完成，重新应用边框: ${container.width}x${container.height}")
                    applyCornerRadius() // 递归调用，此时容器应该有正确尺寸
                }
            })
            return
        }

        if (clipBounds != null) {
            // 🎯 父容器边框法：使用父容器边框而不是扩展clipBounds
            if (isBorderEnabled) {
                createUnifiedBorderView(clipBounds)
            } else {
                removeUnifiedBorderView()
            }

            // 使用原始clipBounds信息（不扩展）
            val currentDisplayWidth = clipBounds.width()
            val currentDisplayHeight = clipBounds.height()
            val currentOffsetX = clipBounds.left
            val currentOffsetY = clipBounds.top

            // 🎯 关键修复：裁剪状态下同时使用clipBounds和圆角裁剪
            // clipBounds负责矩形区域裁剪，outline负责圆角裁剪

            // 设置基于裁剪区域的圆角outline
            container.outlineProvider = object : ViewOutlineProvider() {
                override fun getOutline(view: View, outline: Outline) {
                    // 🎯 关键：基于clipBounds区域设置圆角outline
                    // 🎯 父容器边框法：只设置圆角，边框由父容器边框视图处理
                    outline.setRoundRect(
                        currentOffsetX,
                        currentOffsetY,
                        currentOffsetX + currentDisplayWidth,
                        currentOffsetY + currentDisplayHeight,
                        radiusPx
                    )
                }
            }
            container.clipToOutline = true // 🎯 启用圆角裁剪

            // 🎯 父容器边框法：不在容器上设置foreground，边框由父容器边框视图处理
            container.foreground = null
            AppLog.v("🎯 [父容器边框法] 裁剪已应用: clipBounds + 圆角outline, 边框由父容器处理")
        } else {
            // 🎯 统一边框法：未裁剪状态也使用父容器边框法
            if (isBorderEnabled) {
                createUnifiedBorderView()
            } else {
                removeUnifiedBorderView()
            }

            // 设置基于整个容器的圆角outline
            container.outlineProvider = object : ViewOutlineProvider() {
                override fun getOutline(view: View, outline: Outline) {
                    outline.setRoundRect(0, 0, view.width, view.height, radiusPx)
                }
            }
            container.clipToOutline = true
            AppLog.v("🎯 [统一边框法] 未裁剪状态已应用: 圆角=${cornerRadius}dp, 边框由父容器处理")
        }

    }





    /**
     * 设置窗口透明度
     */
    fun setWindowAlpha(alphaValue: Float) {
        windowAlpha = alphaValue.coerceIn(0.0f, 1.0f)
        container.alpha = windowAlpha
        AppLog.d("🎯 透明度设置为: ${(windowAlpha * 100).toInt()}%")
    }

    /**
     * 获取当前窗口透明度
     */
    fun getWindowAlpha(): Float = windowAlpha

    /**
     * 设置边框显示状态
     * 🎯 统一边框法：所有窗口都使用父容器边框法
     */
    fun setBorderEnabled(enabled: Boolean) {
        isBorderEnabled = enabled

        // 🎯 统一边框法：无论是否裁剪，都使用父容器边框法
        applyCornerRadius() // 这会触发createUnifiedBorderView或removeUnifiedBorderView

        AppLog.d("🎯 边框显示状态设置为: $enabled (统一边框法)")
    }

    /**
     * 获取当前边框显示状态
     */
    fun isBorderEnabled(): Boolean = isBorderEnabled

    /**
     * 设置边框颜色
     */
    fun setBorderColor(color: Int) {
        borderColor = color
        if (isBorderEnabled) {
            applyCornerRadius() // 重新应用圆角效果以更新边框颜色
        }
        AppLog.d("🎨 边框颜色设置为: ${String.format("#%06X", 0xFFFFFF and color)}")
    }

    /**
     * 获取当前边框颜色
     */
    fun getBorderColor(): Int = borderColor

    /**
     * 设置边框宽度
     * 🎯 统一边框法：边框宽度变化时重新创建边框视图
     */
    fun setBorderWidth(width: Float) {
        borderWidth = width.coerceIn(1f, 20f) // 限制范围在1-20dp之间
        if (isBorderEnabled) {
            // 🎯 统一边框法：边框宽度变化时重新创建边框视图
            applyCornerRadius() // 这会重新创建边框视图以应用新的边框宽度
        }
        AppLog.d("🎯 边框宽度设置为: ${borderWidth}dp (统一边框法)")
    }

    /**
     * 获取当前边框宽度
     */
    fun getBorderWidth(): Float = borderWidth



    /**
     * 清理前景和其他UI状态
     */
    fun cleanup() {
        // 🎯 统一边框法：清理时移除边框视图
        removeUnifiedBorderView()
        container.foreground = null
        AppLog.d("TransformRenderer清理完成")
    }

    /**
     * dp转px工具方法
     */
    private fun dpToPx(dp: Int): Int {
        return TypedValue.applyDimension(
            TypedValue.COMPLEX_UNIT_DIP,
            dp.toFloat(),
            context.resources.displayMetrics
        ).toInt()
    }

    /**
     * 🎯 修复：dp转px工具方法（Float版本，避免精度损失）
     * 与遥控端WindowVisualizationContainerView保持一致
     */
    private fun dpToPxFloat(dp: Float): Float {
        return TypedValue.applyDimension(
            TypedValue.COMPLEX_UNIT_DIP,
            dp,
            context.resources.displayMetrics
        )
    }

    /**
     * 初始化位置管理器（在窗口创建后调用）
     */
    fun initializePositionManager(isCroppedWindow: Boolean, cropRectRatio: RectF?) {
        if (isCroppedWindow && cropRectRatio != null) {
            // 如果当前是裁剪状态，从裁剪位置反推基准位置
            positionManager.syncFromCroppedPosition(cropRectRatio)
        } else {
            // 如果当前不是裁剪状态，同步当前位置为基准位置
            positionManager.syncCurrentAsBase()
        }
        AppLog.d("🎯 位置管理器初始化完成")
    }

    /**
     * 获取位置管理器实例（用于其他组件直接访问）
     */
    fun getPositionManager(): WindowPositionManager {
        return positionManager
    }



    /**
     * 🎯 统一边框法：创建统一边框视图（支持裁剪和未裁剪窗口）
     */
    private fun createUnifiedBorderView(clipBounds: Rect? = null) {
        // 移除现有的边框视图
        removeUnifiedBorderView()

        val parentView = container.parent as? ViewGroup ?: return
        val borderWidthPx = dpToPx(borderWidth.toInt())
        val radiusPx = dpToPxFloat(cornerRadius)

        // 🎯 统一边框法：根据是否裁剪计算边框位置和尺寸
        val (borderLeft, borderTop, borderWidth, borderHeight) = if (clipBounds != null) {
            // 裁剪状态：基于clipBounds计算边框位置
            val left = clipBounds.left - borderWidthPx
            val top = clipBounds.top - borderWidthPx
            val width = clipBounds.width() + borderWidthPx * 2
            val height = clipBounds.height() + borderWidthPx * 2
            arrayOf(left, top, width, height)
        } else {
            // 未裁剪状态：基于整个容器计算边框位置
            val left = -borderWidthPx
            val top = -borderWidthPx
            val width = container.width + borderWidthPx * 2
            val height = container.height + borderWidthPx * 2
            arrayOf(left, top, width, height)
        }

        // 🎥 关键修复：如果边框尺寸无效，不创建边框视图
        if (borderWidth <= 0 || borderHeight <= 0) {
            AppLog.w("🎯 [统一边框法] 边框尺寸无效，跳过创建: ${borderWidth}x${borderHeight}")
            return
        }

        // 创建统一边框视图
        cropBorderView = object : View(context) {
            override fun onDraw(canvas: Canvas) {
                super.onDraw(canvas)

                // 🎯 使用预分配的Paint对象，避免在onDraw中创建对象
                borderPaint.strokeWidth = borderWidthPx.toFloat()
                borderPaint.color = borderColor

                // 🎯 使用预分配的RectF对象，避免在onDraw中创建对象
                borderRect.set(
                    borderWidthPx / 2f,
                    borderWidthPx / 2f,
                    width - borderWidthPx / 2f,
                    height - borderWidthPx / 2f
                )
                // 🎯 统一边框法：边框圆角半径需要加上边框宽度的一半，确保边框内边缘与内容区域外边缘完全贴合
                val borderRadius = radiusPx + borderWidthPx / 2f
                canvas.drawRoundRect(borderRect, borderRadius, borderRadius, borderPaint)

                AppLog.v("🎯 [统一边框法] 边框已绘制: 位置=(${borderLeft}, ${borderTop}), 尺寸=${borderWidth}x${borderHeight}")
            }
        }.apply {
            // 🎯 统一边框法：边框视图使用与容器相同的布局方式
            layoutParams = FrameLayout.LayoutParams(borderWidth, borderHeight).apply {
                leftMargin = borderLeft
                topMargin = borderTop
            }
            // 🎯 统一边框法：边框视图与容器使用相同的坐标系统
            // 设置边框视图的基础位置与容器一致
            x = container.x
            y = container.y
            // 同步容器的变换状态
            translationX = container.translationX
            translationY = container.translationY
            scaleX = container.scaleX
            scaleY = container.scaleY
            rotation = container.rotation
            // 🎯 统一边框法：边框视图的pivot点应该与容器的pivot点在绝对坐标系中一致
            // 容器pivot点在绝对坐标系中的位置 = container.pivotX/Y
            // 边框视图pivot点 = 容器pivot点 - 边框视图的layout偏移
            pivotX = container.pivotX - borderLeft
            pivotY = container.pivotY - borderTop
            // 设置背景透明
            setBackgroundColor(Color.TRANSPARENT)
        }

        // 🎯 关键修复：将边框视图插入到窗口容器的下一个位置，而不是最后位置
        val containerIndex = parentView.indexOfChild(container)
        if (containerIndex >= 0) {
            // 将边框视图插入到窗口容器的下一个位置（窗口容器的正上方）
            parentView.addView(cropBorderView, containerIndex + 1)
            AppLog.d("🎯 [统一边框法] 边框视图已插入到窗口容器的下一个位置: 容器索引=$containerIndex, 边框索引=${containerIndex + 1}")
        } else {
            // 如果找不到容器索引，回退到原有方式
            parentView.addView(cropBorderView)
            AppLog.w("🎯 [统一边框法] 未找到窗口容器索引，使用默认添加方式")
        }
        isCropBorderExpanded = true

        AppLog.d("🎯 [统一边框法] 边框视图已创建: 位置=(${borderLeft}, ${borderTop}), 尺寸=${borderWidth}x${borderHeight}, 裁剪状态=${clipBounds != null}")
    }

    /**
     * 🎯 统一边框法：移除统一边框视图
     */
    private fun removeUnifiedBorderView() {
        cropBorderView?.let { borderView ->
            val parentView = container.parent as? ViewGroup
            parentView?.removeView(borderView)
            cropBorderView = null
            isCropBorderExpanded = false
            AppLog.d("🎯 [统一边框法] 边框视图已移除")
        }
    }

    /**
     * 🎯 统一边框法：更新边框视图位置和变换（当容器位置或变换变化时调用）
     */
    fun updateUnifiedBorderPosition() {
        if (!isCropBorderExpanded || cropBorderView == null) return

        val clipBounds = container.clipBounds
        val borderWidthPx = dpToPx(borderWidth.toInt())

        // 🎯 统一边框法：边框视图与容器完全同步
        cropBorderView!!.apply {
            // 同步基础位置
            x = container.x
            y = container.y
            // 同步变换状态
            translationX = container.translationX
            translationY = container.translationY
            scaleX = container.scaleX
            scaleY = container.scaleY
            rotation = container.rotation

            // 🎯 统一边框法：根据是否裁剪计算pivot点偏移
            val (borderLeft, borderTop) = if (clipBounds != null) {
                // 裁剪状态：基于clipBounds计算偏移
                Pair(clipBounds.left - borderWidthPx, clipBounds.top - borderWidthPx)
            } else {
                // 未裁剪状态：基于容器边缘计算偏移
                Pair(-borderWidthPx, -borderWidthPx)
            }

            // 边框视图pivot点 = 容器pivot点 - 边框视图的layout偏移
            pivotX = container.pivotX - borderLeft
            pivotY = container.pivotY - borderTop
        }

        AppLog.v("🎯 [统一边框法] 边框位置和变换已更新: 容器位置=(${container.x}, ${container.y}), 缩放=${container.scaleX}, 旋转=${container.rotation}°")
    }

    /**
     * 🎯 统一边框法：将边框视图移到前台（层级管理时调用）
     */
    fun bringBorderToFront() {
        cropBorderView?.let { borderView ->
            val parentView = container.parent as? ViewGroup ?: return

            // 🎯 关键修复：将边框视图重新插入到窗口容器的下一个位置
            // 先移除边框视图
            parentView.removeView(borderView)

            // 获取窗口容器的当前索引
            val containerIndex = parentView.indexOfChild(container)
            if (containerIndex >= 0) {
                // 将边框视图重新插入到窗口容器的下一个位置
                parentView.addView(borderView, containerIndex + 1)
                AppLog.d("🎯 [统一边框法] 边框视图已重新插入到窗口容器的下一个位置: 容器索引=$containerIndex, 边框索引=${containerIndex + 1}")
            } else {
                // 如果找不到容器索引，回退到原有方式
                parentView.addView(borderView)
                AppLog.w("🎯 [统一边框法] 未找到窗口容器索引，使用默认添加方式")
            }
        }
    }

    /**
     * 🎯 统一边框法：进入裁剪模式时临时移除边框
     */
    fun onEnterCropMode() {
        // 保存当前边框状态
        borderStateBeforeCrop = isBorderEnabled
        AppLog.d("🎯 [统一边框法] 保存进入裁剪模式前的边框状态: $borderStateBeforeCrop")

        // 如果当前有边框，临时移除边框视图，但不修改isBorderEnabled状态
        if (isBorderEnabled) {
            removeUnifiedBorderView()
            AppLog.d("🎯 [统一边框法] 进入裁剪模式，临时移除边框视图，保持边框状态: $isBorderEnabled")
        } else {
            AppLog.d("🎯 [统一边框法] 进入裁剪模式，当前无边框")
        }
    }

    /**
     * 🎯 统一边框法：退出裁剪模式时恢复边框
     */
    fun onExitCropMode() {
        // 如果进入裁剪模式前有边框，恢复边框视图
        if (borderStateBeforeCrop) {
            // 🎯 修复：重新应用圆角效果来恢复边框视图，不需要修改isBorderEnabled状态
            applyCornerRadius()
            AppLog.d("🎯 [统一边框法] 退出裁剪模式，恢复边框视图，边框状态: isBorderEnabled=$isBorderEnabled")
        } else {
            AppLog.d("🎯 [统一边框法] 退出裁剪模式，进入前无边框，保持无边框状态")
        }

        // 🎯 重置边框状态标记，避免状态混乱
        borderStateBeforeCrop = false
    }

    /**
     * 🎯 统一边框法：同步边框视图的可见性（当窗口显示/隐藏时调用）
     */
    fun syncBorderVisibility(visibility: Int) {
        cropBorderView?.let { borderView ->
            borderView.visibility = visibility
            AppLog.d("🎯 [统一边框法] 边框视图可见性已同步: ${when(visibility) {
                View.VISIBLE -> "VISIBLE"
                View.INVISIBLE -> "INVISIBLE"
                View.GONE -> "GONE"
                else -> "UNKNOWN($visibility)"
            }}")
        }
    }

    // ========== 🎯 横竖屏适配功能 ==========

    /**
     * 🎯 横屏适配：应用方向变换（动态分辨率版）
     */
    private fun applyOrientationTransform() {
        if (currentVideoOrientation == android.content.res.Configuration.ORIENTATION_LANDSCAPE) {
            // 🎯 动态分辨率方案：支持不同发送端设备的分辨率

            val containerWidth = windowWidth.toFloat()
            val containerHeight = windowHeight.toFloat()

            // 容器中心点
            val centerX = containerWidth / 2
            val centerY = containerHeight / 2

            // 🎯 获取当前视频的实际分辨率
            val videoWidth = if (currentVideoWidth > 0) currentVideoWidth else {
                // 回退：从容器尺寸反推原始分辨率（容器 = 原始 × 0.4）
                (containerWidth / 0.4f).toInt()
            }
            val videoHeight = if (currentVideoHeight > 0) currentVideoHeight else {
                // 回退：从容器尺寸反推原始分辨率（容器 = 原始 × 0.4）
                (containerHeight / 0.4f).toInt()
            }

            AppLog.d("🎯 使用视频分辨率: ${videoWidth}×${videoHeight}, 容器尺寸: ${containerWidth.toInt()}×${containerHeight.toInt()}")

            // 🎯 步骤1：恢复原始比例（抵消TextureView变形）
            // TextureView将横屏视频强制适配到竖屏容器，我们需要先恢复原始比例
            val originalScaleX = videoWidth.toFloat() / containerWidth
            val originalScaleY = videoHeight.toFloat() / containerHeight
            transformMatrix.postScale(originalScaleX, originalScaleY, centerX, centerY)

            // 🎯 步骤2：旋转90度（横屏 → 竖屏方向）
            transformMatrix.postRotate(90f, centerX, centerY)

            // 🎯 步骤3：正确缩放到容器尺寸
            // 旋转后：videoWidth×videoHeight → videoHeight×videoWidth
            // 目标：适配到containerWidth×containerHeight
            val finalScaleX = containerWidth / videoHeight.toFloat()
            val finalScaleY = containerHeight / videoWidth.toFloat()
            val finalScale = minOf(finalScaleX, finalScaleY) // 使用较小值确保完全适配
            transformMatrix.postScale(finalScale, finalScale, centerX, centerY)

            AppLog.d("🎯 横屏适配变换（动态分辨率版）:")
            AppLog.d("🎯 步骤1: 恢复原始比例 scaleX=${originalScaleX}, scaleY=${originalScaleY}")
            AppLog.d("🎯 步骤2: 旋转90°")
            AppLog.d("🎯 步骤3: 最终缩放 ${finalScale}倍 (scaleX=${finalScaleX}, scaleY=${finalScaleY})")
            AppLog.d("🎯 变换过程: ${videoWidth}×${videoHeight} → 恢复比例 → 旋转90° → ${videoHeight}×${videoWidth} → 缩放${finalScale} → ${containerWidth.toInt()}×${containerHeight.toInt()}")
        }
    }

    /**
     * 设置视频方向和分辨率
     */
    fun setVideoOrientation(orientation: Int, videoWidth: Int = 0, videoHeight: Int = 0) {
        if (currentVideoOrientation != orientation) {
            AppLog.d("🎯 视频方向变化: ${getOrientationName(currentVideoOrientation)} -> ${getOrientationName(orientation)}")
            currentVideoOrientation = orientation
        }

        // 🎯 更新视频分辨率信息
        if (videoWidth > 0 && videoHeight > 0) {
            currentVideoWidth = videoWidth
            currentVideoHeight = videoHeight
            AppLog.d("🎯 视频分辨率已更新: ${videoWidth}×${videoHeight}")
        }
    }

    /**
     * 获取方向名称（用于日志）
     */
    private fun getOrientationName(orientation: Int): String {
        return when (orientation) {
            android.content.res.Configuration.ORIENTATION_LANDSCAPE -> "横屏"
            android.content.res.Configuration.ORIENTATION_PORTRAIT -> "竖屏"
            else -> "未知($orientation)"
        }
    }

}
