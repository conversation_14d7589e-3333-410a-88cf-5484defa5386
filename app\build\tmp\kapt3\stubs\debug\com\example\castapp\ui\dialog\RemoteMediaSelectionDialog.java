package com.example.castapp.ui.dialog;

/**
 * 简单的媒体选择对话框
 */
@kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000\u0084\u0001\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010 \n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\u000e\n\u0000\n\u0002\u0018\u0002\n\u0002\u0010\b\n\u0000\n\u0002\u0010\u0006\n\u0002\b\u0004\n\u0002\u0010\u000b\n\u0002\b\u0003\n\u0002\u0010$\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0006\u0018\u00002\u00020\u0001:\u000212B\u0015\u0012\u0006\u0010\u0002\u001a\u00020\u0003\u0012\u0006\u0010\u0004\u001a\u00020\u0005\u00a2\u0006\u0002\u0010\u0006J\u0010\u0010\u000b\u001a\u00020\f2\u0006\u0010\r\u001a\u00020\u000eH\u0002J\n\u0010\u000f\u001a\u0004\u0018\u00010\u0010H\u0002J\u001c\u0010\u0011\u001a\b\u0012\u0004\u0012\u00020\u00130\u00122\f\u0010\u0014\u001a\b\u0012\u0004\u0012\u00020\u00130\u0012H\u0002J\u001a\u0010\u0015\u001a\u0004\u0018\u00010\u00162\u0006\u0010\u0017\u001a\u00020\u00182\u0006\u0010\u0019\u001a\u00020\u001aH\u0002J\u0014\u0010\u001b\u001a\u000e\u0012\u0004\u0012\u00020\u001d\u0012\u0004\u0012\u00020\u001d0\u001cH\u0002J\b\u0010\u001e\u001a\u00020\u001fH\u0002J\u0010\u0010 \u001a\u00020\f2\u0006\u0010\r\u001a\u00020\u000eH\u0002J\u0010\u0010!\u001a\u00020\f2\u0006\u0010\r\u001a\u00020\u000eH\u0002J\u0010\u0010\"\u001a\u00020\f2\u0006\u0010\r\u001a\u00020\u000eH\u0002J\b\u0010#\u001a\u00020$H\u0002J.\u0010%\u001a\u00020\f2\u0006\u0010\r\u001a\u00020\u001a2\u0006\u0010&\u001a\u00020\u001a2\u0014\b\u0002\u0010\'\u001a\u000e\u0012\u0004\u0012\u00020\u001a\u0012\u0004\u0012\u00020\u00010(H\u0002J\u0018\u0010)\u001a\u00020\f2\u0006\u0010*\u001a\u00020+2\u0006\u0010,\u001a\u00020-H\u0002J\u0006\u0010.\u001a\u00020\fJ\u0016\u0010/\u001a\u00020\f2\f\u00100\u001a\b\u0012\u0004\u0012\u00020\u00130\u0012H\u0002R\u000e\u0010\u0007\u001a\u00020\bX\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0002\u001a\u00020\u0003X\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0004\u001a\u00020\u0005X\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u000e\u0010\t\u001a\u00020\nX\u0082\u0004\u00a2\u0006\u0002\n\u0000\u00a8\u00063"}, d2 = {"Lcom/example/castapp/ui/dialog/RemoteMediaSelectionDialog;", "", "context", "Landroid/content/Context;", "remoteReceiverConnection", "Lcom/example/castapp/model/RemoteReceiverConnection;", "(Landroid/content/Context;Lcom/example/castapp/model/RemoteReceiverConnection;)V", "connectionManager", "Lcom/example/castapp/manager/RemoteConnectionManager;", "remoteReceiverManager", "Lcom/example/castapp/manager/RemoteReceiverManager;", "createCameraPlaceholder", "", "mediaType", "Lcom/example/castapp/ui/dialog/RemoteMediaSelectionDialog$MediaType;", "findActiveControlDialog", "Lcom/example/castapp/ui/dialog/RemoteReceiverControlDialog;", "getCurrentWindowPositions", "", "Lcom/example/castapp/model/CastWindowInfo;", "existingWindows", "getCurrentWindowTransform", "Lcom/example/castapp/ui/dialog/RemoteMediaSelectionDialog$WindowTransformState;", "visualizationView", "Lcom/example/castapp/ui/view/WindowContainerVisualizationView;", "connectionId", "", "getReceiverScreenResolution", "Lkotlin/Pair;", "", "getRemoteControlScale", "", "handleLocalContainerMode", "handleMediaSelection", "handleRealTimeSyncMode", "isRealTimeSyncEnabled", "", "sendAddMediaMessage", "displayName", "extraData", "", "setupClickListeners", "dialogView", "Landroid/view/View;", "dialog", "Landroid/app/AlertDialog;", "show", "triggerVisualizationUpdate", "windowInfoList", "MediaType", "WindowTransformState", "app_debug"})
public final class RemoteMediaSelectionDialog {
    @org.jetbrains.annotations.NotNull()
    private final android.content.Context context = null;
    @org.jetbrains.annotations.NotNull()
    private final com.example.castapp.model.RemoteReceiverConnection remoteReceiverConnection = null;
    @org.jetbrains.annotations.NotNull()
    private final com.example.castapp.manager.RemoteReceiverManager remoteReceiverManager = null;
    @org.jetbrains.annotations.NotNull()
    private final com.example.castapp.manager.RemoteConnectionManager connectionManager = null;
    
    public RemoteMediaSelectionDialog(@org.jetbrains.annotations.NotNull()
    android.content.Context context, @org.jetbrains.annotations.NotNull()
    com.example.castapp.model.RemoteReceiverConnection remoteReceiverConnection) {
        super();
    }
    
    /**
     * 显示媒体选择对话框
     */
    public final void show() {
    }
    
    /**
     * 设置点击监听器
     */
    private final void setupClickListeners(android.view.View dialogView, android.app.AlertDialog dialog) {
    }
    
    /**
     * 处理媒体选择
     */
    private final void handleMediaSelection(com.example.castapp.ui.dialog.RemoteMediaSelectionDialog.MediaType mediaType) {
    }
    
    /**
     * 处理实时同步模式
     */
    private final void handleRealTimeSyncMode(com.example.castapp.ui.dialog.RemoteMediaSelectionDialog.MediaType mediaType) {
    }
    
    /**
     * 处理本地容器模式
     */
    private final void handleLocalContainerMode(com.example.castapp.ui.dialog.RemoteMediaSelectionDialog.MediaType mediaType) {
    }
    
    /**
     * 创建摄像头占位容器
     */
    private final void createCameraPlaceholder(com.example.castapp.ui.dialog.RemoteMediaSelectionDialog.MediaType mediaType) {
    }
    
    /**
     * 发送添加媒体消息
     */
    private final void sendAddMediaMessage(java.lang.String mediaType, java.lang.String displayName, java.util.Map<java.lang.String, ? extends java.lang.Object> extraData) {
    }
    
    /**
     * 检查实时同步状态
     */
    private final boolean isRealTimeSyncEnabled() {
        return false;
    }
    
    /**
     * 获取接收端屏幕分辨率
     */
    private final kotlin.Pair<java.lang.Integer, java.lang.Integer> getReceiverScreenResolution() {
        return null;
    }
    
    /**
     * 获取遥控端窗口缩放倍数
     * 🎯 简化：使用默认缩放倍数，避免复杂计算
     */
    private final double getRemoteControlScale() {
        return 0.0;
    }
    
    /**
     * 🎯 核心修复：获取现有窗口的当前实际变换状态
     * 保持遥控端可视化窗口的当前位置和变换状态，而不是转换为接收端坐标
     */
    private final java.util.List<com.example.castapp.model.CastWindowInfo> getCurrentWindowPositions(java.util.List<com.example.castapp.model.CastWindowInfo> existingWindows) {
        return null;
    }
    
    /**
     * 🎯 获取指定窗口的当前完整变换状态
     */
    private final com.example.castapp.ui.dialog.RemoteMediaSelectionDialog.WindowTransformState getCurrentWindowTransform(com.example.castapp.ui.view.WindowContainerVisualizationView visualizationView, java.lang.String connectionId) {
        return null;
    }
    
    /**
     * 🎯 查找当前活跃的控制对话框
     */
    private final com.example.castapp.ui.dialog.RemoteReceiverControlDialog findActiveControlDialog() {
        return null;
    }
    
    /**
     * 触发可视化更新
     * 🎯 关键修复：通知RemoteReceiverControlDialog更新窗口显示
     */
    private final void triggerVisualizationUpdate(java.util.List<com.example.castapp.model.CastWindowInfo> windowInfoList) {
    }
    
    @kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000\u0012\n\u0002\u0018\u0002\n\u0002\u0010\u0010\n\u0000\n\u0002\u0010\u000e\n\u0002\b\u000b\b\u0086\u0081\u0002\u0018\u00002\b\u0012\u0004\u0012\u00020\u00000\u0001B\u0017\b\u0002\u0012\u0006\u0010\u0002\u001a\u00020\u0003\u0012\u0006\u0010\u0004\u001a\u00020\u0003\u00a2\u0006\u0002\u0010\u0005R\u0011\u0010\u0002\u001a\u00020\u0003\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0006\u0010\u0007R\u0011\u0010\u0004\u001a\u00020\u0003\u00a2\u0006\b\n\u0000\u001a\u0004\b\b\u0010\u0007j\u0002\b\tj\u0002\b\nj\u0002\b\u000bj\u0002\b\fj\u0002\b\r\u00a8\u0006\u000e"}, d2 = {"Lcom/example/castapp/ui/dialog/RemoteMediaSelectionDialog$MediaType;", "", "displayName", "", "messageType", "(Ljava/lang/String;ILjava/lang/String;Ljava/lang/String;)V", "getDisplayName", "()Ljava/lang/String;", "getMessageType", "FRONT_CAMERA", "REAR_CAMERA", "VIDEO", "PICTURE", "TEXT", "app_debug"})
    public static enum MediaType {
        /*public static final*/ FRONT_CAMERA /* = new FRONT_CAMERA(null, null) */,
        /*public static final*/ REAR_CAMERA /* = new REAR_CAMERA(null, null) */,
        /*public static final*/ VIDEO /* = new VIDEO(null, null) */,
        /*public static final*/ PICTURE /* = new PICTURE(null, null) */,
        /*public static final*/ TEXT /* = new TEXT(null, null) */;
        @org.jetbrains.annotations.NotNull()
        private final java.lang.String displayName = null;
        @org.jetbrains.annotations.NotNull()
        private final java.lang.String messageType = null;
        
        MediaType(java.lang.String displayName, java.lang.String messageType) {
        }
        
        @org.jetbrains.annotations.NotNull()
        public final java.lang.String getDisplayName() {
            return null;
        }
        
        @org.jetbrains.annotations.NotNull()
        public final java.lang.String getMessageType() {
            return null;
        }
        
        @org.jetbrains.annotations.NotNull()
        public static kotlin.enums.EnumEntries<com.example.castapp.ui.dialog.RemoteMediaSelectionDialog.MediaType> getEntries() {
            return null;
        }
    }
    
    /**
     * 🎯 变换状态数据类
     */
    @kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000.\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0000\n\u0002\u0010\u0007\n\u0002\b\u0004\n\u0002\u0010\u000b\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\b\u0014\n\u0002\u0010\b\n\u0000\n\u0002\u0010\u000e\n\u0000\b\u0082\b\u0018\u00002\u00020\u0001B?\u0012\u0006\u0010\u0002\u001a\u00020\u0003\u0012\u0006\u0010\u0004\u001a\u00020\u0003\u0012\u0006\u0010\u0005\u001a\u00020\u0003\u0012\u0006\u0010\u0006\u001a\u00020\u0003\u0012\u0006\u0010\u0007\u001a\u00020\b\u0012\u0006\u0010\t\u001a\u00020\b\u0012\b\u0010\n\u001a\u0004\u0018\u00010\u000b\u00a2\u0006\u0002\u0010\fJ\t\u0010\u0015\u001a\u00020\u0003H\u00c6\u0003J\t\u0010\u0016\u001a\u00020\u0003H\u00c6\u0003J\t\u0010\u0017\u001a\u00020\u0003H\u00c6\u0003J\t\u0010\u0018\u001a\u00020\u0003H\u00c6\u0003J\t\u0010\u0019\u001a\u00020\bH\u00c6\u0003J\t\u0010\u001a\u001a\u00020\bH\u00c6\u0003J\u000b\u0010\u001b\u001a\u0004\u0018\u00010\u000bH\u00c6\u0003JQ\u0010\u001c\u001a\u00020\u00002\b\b\u0002\u0010\u0002\u001a\u00020\u00032\b\b\u0002\u0010\u0004\u001a\u00020\u00032\b\b\u0002\u0010\u0005\u001a\u00020\u00032\b\b\u0002\u0010\u0006\u001a\u00020\u00032\b\b\u0002\u0010\u0007\u001a\u00020\b2\b\b\u0002\u0010\t\u001a\u00020\b2\n\b\u0002\u0010\n\u001a\u0004\u0018\u00010\u000bH\u00c6\u0001J\u0013\u0010\u001d\u001a\u00020\b2\b\u0010\u001e\u001a\u0004\u0018\u00010\u0001H\u00d6\u0003J\t\u0010\u001f\u001a\u00020 H\u00d6\u0001J\t\u0010!\u001a\u00020\"H\u00d6\u0001R\u0013\u0010\n\u001a\u0004\u0018\u00010\u000b\u00a2\u0006\b\n\u0000\u001a\u0004\b\r\u0010\u000eR\u0011\u0010\t\u001a\u00020\b\u00a2\u0006\b\n\u0000\u001a\u0004\b\t\u0010\u000fR\u0011\u0010\u0007\u001a\u00020\b\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0007\u0010\u000fR\u0011\u0010\u0006\u001a\u00020\u0003\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0010\u0010\u0011R\u0011\u0010\u0005\u001a\u00020\u0003\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0012\u0010\u0011R\u0011\u0010\u0002\u001a\u00020\u0003\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0013\u0010\u0011R\u0011\u0010\u0004\u001a\u00020\u0003\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0014\u0010\u0011\u00a8\u0006#"}, d2 = {"Lcom/example/castapp/ui/dialog/RemoteMediaSelectionDialog$WindowTransformState;", "", "visualizedX", "", "visualizedY", "scale", "rotation", "isMirrored", "", "isCropping", "cropRectRatio", "Landroid/graphics/RectF;", "(FFFFZZLandroid/graphics/RectF;)V", "getCropRectRatio", "()Landroid/graphics/RectF;", "()Z", "getRotation", "()F", "getScale", "getVisualizedX", "getVisualizedY", "component1", "component2", "component3", "component4", "component5", "component6", "component7", "copy", "equals", "other", "hashCode", "", "toString", "", "app_debug"})
    static final class WindowTransformState {
        private final float visualizedX = 0.0F;
        private final float visualizedY = 0.0F;
        private final float scale = 0.0F;
        private final float rotation = 0.0F;
        private final boolean isMirrored = false;
        private final boolean isCropping = false;
        @org.jetbrains.annotations.Nullable()
        private final android.graphics.RectF cropRectRatio = null;
        
        public WindowTransformState(float visualizedX, float visualizedY, float scale, float rotation, boolean isMirrored, boolean isCropping, @org.jetbrains.annotations.Nullable()
        android.graphics.RectF cropRectRatio) {
            super();
        }
        
        public final float getVisualizedX() {
            return 0.0F;
        }
        
        public final float getVisualizedY() {
            return 0.0F;
        }
        
        public final float getScale() {
            return 0.0F;
        }
        
        public final float getRotation() {
            return 0.0F;
        }
        
        public final boolean isMirrored() {
            return false;
        }
        
        public final boolean isCropping() {
            return false;
        }
        
        @org.jetbrains.annotations.Nullable()
        public final android.graphics.RectF getCropRectRatio() {
            return null;
        }
        
        public final float component1() {
            return 0.0F;
        }
        
        public final float component2() {
            return 0.0F;
        }
        
        public final float component3() {
            return 0.0F;
        }
        
        public final float component4() {
            return 0.0F;
        }
        
        public final boolean component5() {
            return false;
        }
        
        public final boolean component6() {
            return false;
        }
        
        @org.jetbrains.annotations.Nullable()
        public final android.graphics.RectF component7() {
            return null;
        }
        
        @org.jetbrains.annotations.NotNull()
        public final com.example.castapp.ui.dialog.RemoteMediaSelectionDialog.WindowTransformState copy(float visualizedX, float visualizedY, float scale, float rotation, boolean isMirrored, boolean isCropping, @org.jetbrains.annotations.Nullable()
        android.graphics.RectF cropRectRatio) {
            return null;
        }
        
        @java.lang.Override()
        public boolean equals(@org.jetbrains.annotations.Nullable()
        java.lang.Object other) {
            return false;
        }
        
        @java.lang.Override()
        public int hashCode() {
            return 0;
        }
        
        @java.lang.Override()
        @org.jetbrains.annotations.NotNull()
        public java.lang.String toString() {
            return null;
        }
    }
}