package com.example.castapp.manager

import android.content.Context
import android.os.Handler
import android.os.Looper
import android.widget.Toast
import com.example.castapp.model.RemoteReceiverConnection
import com.example.castapp.remote.RemoteSenderWebSocketClient
import com.example.castapp.ui.dialog.RemoteReceiverControlDialog
import com.example.castapp.ui.dialog.RemoteReceiverSettingsControlDialog
import com.example.castapp.utils.AppLog
import com.example.castapp.websocket.ControlMessage
import java.lang.ref.WeakReference

/**
 * 🐾 接收端管理器
 * 负责处理所有接收端相关的业务逻辑，包括连接管理、消息处理和控制对话框管理
 *
 * 主要功能：
 * - 接收端设备的连接和断开
 * - 接收端消息的处理和分发
 * - 接收端控制对话框的管理
 * - 与 RemoteConnectionManager 协作进行状态管理
 *
 * 🔧 修复：改为单例模式，确保全局唯一实例
 */
class RemoteReceiverManager private constructor(
    private val connectionManager: RemoteConnectionManager = RemoteConnectionManager.getInstance()
) {

    companion object {
        @Volatile
        private var INSTANCE: RemoteReceiverManager? = null

        fun getInstance(): RemoteReceiverManager {
            return INSTANCE ?: synchronized(this) {
                INSTANCE ?: RemoteReceiverManager().also { INSTANCE = it }
            }
        }
    }

    // 🚀 活跃的接收端控制对话框（用于被动状态通知）
    private val activeReceiverControlDialogs = mutableMapOf<String, WeakReference<RemoteReceiverControlDialog>>()

    // 🎛️ 新增：活跃的接收端设置控制对话框（用于设置同步通知）
    private val activeReceiverSettingsControlDialogs = mutableMapOf<String, WeakReference<RemoteReceiverSettingsControlDialog>>()

    // 📐 分辨率缓存：存储已获取的接收端分辨率信息
    private val cachedScreenResolutions = mutableMapOf<String, Pair<Int, Int>>()

    // 🐾 UI线程Handler，用于在UI线程显示Toast
    private val uiHandler = Handler(Looper.getMainLooper())

    /**
     * 连接到接收端设备
     */
    fun connectReceiver(
        receiver: RemoteReceiverConnection,
        context: Context,
        onConnectionStateChanged: ((RemoteReceiverConnection, Boolean) -> Unit)? = null
    ): Boolean {
        // 检查是否已连接
        val existingClient = connectionManager.getReceiverClient(receiver.id)
        if (existingClient?.getConnectionStatus() == true) {
            Toast.makeText(context, "设备已连接", Toast.LENGTH_SHORT).show()
            return true
        }

        // 清理旧连接
        if (existingClient != null) {
            connectionManager.removeReceiverClient(receiver.id)
        }

        // 创建新连接
        val client = RemoteSenderWebSocketClient(
            targetIp = receiver.ipAddress,
            targetPort = receiver.port,
            deviceName = receiver.deviceName,
            onMessageReceived = { message ->
                handleReceiverMessage(receiver, message, onConnectionStateChanged)
            },
            onConnectionStateChanged = { isConnected ->
                handleReceiverConnectionStateChanged(receiver, isConnected, context, onConnectionStateChanged)
            }
        )

        // 尝试连接
        return if (client.connect()) {
            connectionManager.addReceiverClient(receiver.id, client)
            showToastOnUiThread(context, "正在连接到：${receiver.deviceName}")
            true
        } else {
            showToastOnUiThread(context, "连接失败：${receiver.deviceName}")
            false
        }
    }

    /**
     * 断开接收端连接
     */
    fun disconnectReceiver(
        receiver: RemoteReceiverConnection,
        context: Context,
        onConnectionStateChanged: ((RemoteReceiverConnection, Boolean) -> Unit)? = null
    ) {
        disconnectReceiverWithType(receiver, true, "用户主动断开", context, onConnectionStateChanged)
    }

    /**
     * 断开接收端连接（带类型）
     */
    fun disconnectReceiverWithType(
        receiver: RemoteReceiverConnection,
        sendMessage: Boolean,
        reason: String = "",
        context: Context,
        onConnectionStateChanged: ((RemoteReceiverConnection, Boolean) -> Unit)? = null
    ) {
        AppLog.d("断开接收端: ${receiver.deviceName}, 原因: $reason")

        // 发送断开消息（如果需要）
        val client = connectionManager.getReceiverClient(receiver.id)
        if (client != null && sendMessage && client.getConnectionStatus()) {
            try {
                // 🔧 修复：使用WebSocket连接的实际连接ID，而不是receiver.id
                val actualConnectionId = client.getActualConnectionId()
                val disconnectMessage = ControlMessage.createDisconnect(actualConnectionId)
                client.sendMessage(disconnectMessage)
                AppLog.d("发送断开消息使用连接ID: $actualConnectionId (而非receiver.id: ${receiver.id})")
                Thread.sleep(50)
            } catch (e: Exception) {
                AppLog.w("发送断开消息失败", e)
            }
        }

        // 📐 清理缓存的分辨率信息
        cachedScreenResolutions.remove(receiver.id)
        AppLog.d("📐 主动断开连接，已清理分辨率缓存: ${receiver.deviceName}")

        // 🎯 清理全局遥控端文字窗口管理器
        clearGlobalRemoteTextWindowManagers(receiver.id)

        // 更新状态
        val disconnectedReceiver = receiver.withConnectionState(false)
        onConnectionStateChanged?.invoke(disconnectedReceiver, false)
        notifyReceiverControlDialogs(disconnectedReceiver)

        // 清理连接
        connectionManager.removeReceiverClient(receiver.id)

        showToastOnUiThread(context, "已断开连接：${receiver.deviceName}")
    }

    /**
     * 注册接收端控制对话框，用于接收连接状态变化通知
     */
    fun registerReceiverControlDialog(receiverId: String, dialog: RemoteReceiverControlDialog) {
        activeReceiverControlDialogs[receiverId] = WeakReference(dialog)
        AppLog.d("注册接收端控制对话框: $receiverId")
    }

    /**
     * 注销接收端控制对话框
     */
    fun unregisterReceiverControlDialog(receiverId: String) {
        activeReceiverControlDialogs.remove(receiverId)
        AppLog.d("注销接收端控制对话框: $receiverId")
    }

    /**
     * 处理接收端连接状态变化
     */
    private fun handleReceiverConnectionStateChanged(
        receiver: RemoteReceiverConnection,
        isConnected: Boolean,
        context: Context,
        onConnectionStateChanged: ((RemoteReceiverConnection, Boolean) -> Unit)?
    ) {
        val updatedReceiver = receiver.withConnectionState(isConnected)
        onConnectionStateChanged?.invoke(updatedReceiver, isConnected)
        notifyReceiverControlDialogs(updatedReceiver)

        val message = if (isConnected) {
            "已连接到：${receiver.deviceName}"
        } else {
            // 📐 连接断开时清理缓存的分辨率信息
            cachedScreenResolutions.remove(receiver.id)
            AppLog.d("📐 连接断开，已清理分辨率缓存: ${receiver.deviceName}")

            // 🎯 清理全局遥控端文字窗口管理器
            clearGlobalRemoteTextWindowManagers(receiver.id)

            connectionManager.removeReceiverClient(receiver.id)
            "与${receiver.deviceName}的连接已断开"
        }

        // 🐾 修复：在UI线程显示Toast
        showToastOnUiThread(context, message)
    }

    /**
     * 通知所有相关的控制对话框连接状态变化
     */
    private fun notifyReceiverControlDialogs(receiver: RemoteReceiverConnection) {
        val dialogRef = activeReceiverControlDialogs[receiver.id]
        val dialog = dialogRef?.get()
        if (dialog != null) {
            dialog.onConnectionStateChanged(receiver)
            AppLog.d("已通知控制对话框连接状态变化: ${receiver.deviceName} -> ${receiver.isConnected}")
        } else {
            // 清理无效的弱引用
            activeReceiverControlDialogs.remove(receiver.id)
        }
    }

    /**
     * 🚀 架构修复：处理接收端设备消息（仅处理遥控管理相关消息）
     * 发送端音视频控制消息由 MessageReceivingManager 处理，避免功能混淆
     */
    private fun handleReceiverMessage(
        receiver: RemoteReceiverConnection,
        message: ControlMessage,
        onConnectionStateChanged: ((RemoteReceiverConnection, Boolean) -> Unit)?
    ) {
        AppLog.d("处理接收端设备消息: ${message.type} from ${receiver.deviceName}")

        when (message.type) {
            ControlMessage.TYPE_CONNECTION_RESPONSE -> {
                handleConnectionResponse(receiver, message, onConnectionStateChanged)
            }

            ControlMessage.TYPE_SCREEN_RESOLUTION -> {
                handleScreenResolution(receiver, message, onConnectionStateChanged)
            }

            ControlMessage.TYPE_REMOTE_CONTROL_SERVICE_STOPPED -> {
                handleRemoteControlServiceStopped(receiver, message, onConnectionStateChanged)
            }

            ControlMessage.TYPE_DISCONNECT -> {
                handleDisconnectMessage(receiver, onConnectionStateChanged)
            }

            // 🎛️ 新增：处理远程接收端设置同步消息
            ControlMessage.TYPE_REMOTE_RECEIVER_SETTINGS_SYNC -> {
                handleRemoteReceiverSettingsSync(receiver, message)
            }

            // 🔄 新增：处理接收端到遥控端的反向同步消息
            ControlMessage.TYPE_RECEIVER_LOCAL_AUDIO_VIDEO_CHANGED -> {
                handleReceiverLocalAudioVideoChanged(receiver, message)
            }

            ControlMessage.TYPE_RECEIVER_LOCAL_PLAYBACK_MODE_CHANGED -> {
                handleReceiverLocalPlaybackModeChanged(receiver, message)
            }

            ControlMessage.TYPE_RECEIVER_LOCAL_VOLUME_CHANGED -> {
                handleReceiverLocalVolumeChanged(receiver, message)
            }

            ControlMessage.TYPE_RECEIVER_LOCAL_SETTINGS_BROADCAST -> {
                handleReceiverLocalSettingsBroadcast(receiver, message)
            }

            // 🪟 新增：处理投屏窗口管理响应消息
            ControlMessage.TYPE_WINDOW_MANAGER_RESPONSE -> {
                handleWindowManagerResponse(receiver, message)
            }

            // 📸 新增：处理截图响应消息
            ControlMessage.TYPE_SCREENSHOT_RESPONSE -> {
                handleScreenshotResponse(receiver, message)
            }

            ControlMessage.TYPE_SCREENSHOT_ERROR -> {
                handleScreenshotError(receiver, message)
            }

            // 📝 新增：处理文字内容响应消息
            ControlMessage.TYPE_TEXT_CONTENT_RESPONSE -> {
                handleTextContentResponse(receiver, message)
            }

            ControlMessage.TYPE_TEXT_CONTENT_ERROR -> {
                handleTextContentError(receiver, message)
            }

            // 📐 新增：处理屏幕分辨率响应消息
            ControlMessage.TYPE_SCREEN_RESOLUTION_RESPONSE -> {
                handleScreenResolutionResponse(receiver, message, onConnectionStateChanged)
            }

            // 📱 新增：处理摄像头窗口创建通知消息
            "camera_window_created" -> {
                handleCameraWindowCreatedNotification(receiver, message)
            }

            else -> {
                // 🚀 架构修复：只记录遥控管理相关的未处理消息，避免日志混淆
                if (message.type.startsWith("remote_")) {
                    AppLog.d("未处理的遥控管理消息类型: ${message.type}")
                }
                // 发送端音视频控制消息不在此处记录，由 MessageReceivingManager 处理
            }
        }
    }

    /**
     * 处理连接响应消息
     */
    private fun handleConnectionResponse(
        receiver: RemoteReceiverConnection,
        message: ControlMessage,
        onConnectionStateChanged: ((RemoteReceiverConnection, Boolean) -> Unit)?
    ) {
        val success = message.getBooleanData("success") ?: false
        val responseMessage = message.getStringData("message") ?: ""
        AppLog.d("接收端连接响应: success=$success, message=$responseMessage")

        if (success) {
            // 连接成功，更新接收端状态
            val updatedReceiver = receiver.withConnectionState(true)
            onConnectionStateChanged?.invoke(updatedReceiver, true)
            AppLog.d("接收端连接成功: ${receiver.deviceName}")
        } else {
            AppLog.w("接收端连接失败: ${receiver.deviceName}, 原因: $responseMessage")
        }
    }

    /**
     * 处理屏幕分辨率消息
     */
    private fun handleScreenResolution(
        receiver: RemoteReceiverConnection,
        message: ControlMessage,
        onConnectionStateChanged: ((RemoteReceiverConnection, Boolean) -> Unit)?
    ) {
        // 🚀 遥控管理专用：处理接收端屏幕分辨率信息（用于计算远程控制窗口尺寸）
        // 注意：这与发送端音视频功能的分辨率处理是独立的
        val width = message.getIntData("width") ?: 0
        val height = message.getIntData("height") ?: 0

        if (width > 0 && height > 0) {
            AppLog.d("收到接收端屏幕分辨率: ${width}×${height} from ${receiver.deviceName}")

            // 更新RemoteReceiver对象的屏幕分辨率，用于遥控窗口尺寸计算
            val updatedReceiver = receiver.withScreenResolution(width, height)
                .withConnectionState(receiver.isConnected) // 保持原有连接状态

            // 更新状态
            onConnectionStateChanged?.invoke(updatedReceiver, receiver.isConnected)

            AppLog.d("已更新接收端屏幕分辨率: ${receiver.deviceName} -> ${width}×${height}, 连接状态: ${updatedReceiver.isConnected}")
        } else {
            AppLog.w("收到无效的屏幕分辨率信息: ${width}×${height}")
        }
    }

    /**
     * 处理远程被控服务停止消息
     */
    private fun handleRemoteControlServiceStopped(
        receiver: RemoteReceiverConnection,
        message: ControlMessage,
        onConnectionStateChanged: ((RemoteReceiverConnection, Boolean) -> Unit)?
    ) {
        val reason = message.getStringData("reason") ?: "远程被控服务已停止"
        AppLog.d("接收端远程被控服务停止: ${receiver.deviceName}, 原因: $reason")

        // 📐 清理缓存的分辨率信息
        cachedScreenResolutions.remove(receiver.id)
        AppLog.d("📐 已清理断开连接设备的分辨率缓存: ${receiver.deviceName}")

        // 🎯 清理全局遥控端文字窗口管理器
        clearGlobalRemoteTextWindowManagers(receiver.id)

        val disconnectedReceiver = receiver.withConnectionState(false)
        onConnectionStateChanged?.invoke(disconnectedReceiver, false)
        notifyReceiverControlDialogs(disconnectedReceiver)
        connectionManager.removeReceiverClient(receiver.id)
    }

    /**
     * 处理断开连接消息
     */
    private fun handleDisconnectMessage(
        receiver: RemoteReceiverConnection,
        onConnectionStateChanged: ((RemoteReceiverConnection, Boolean) -> Unit)?
    ) {
        AppLog.d("接收端收到断开连接消息: ${receiver.deviceName}")

        // 🎯 清理全局遥控端文字窗口管理器
        clearGlobalRemoteTextWindowManagers(receiver.id)

        val disconnectedReceiver = receiver.withConnectionState(false)
        onConnectionStateChanged?.invoke(disconnectedReceiver, false)
        notifyReceiverControlDialogs(disconnectedReceiver)
        connectionManager.removeReceiverClient(receiver.id)
    }

    /**
     * 🐾 在UI线程安全地显示Toast
     */
    private fun showToastOnUiThread(context: Context, message: String) {
        uiHandler.post {
            try {
                Toast.makeText(context, message, Toast.LENGTH_SHORT).show()
            } catch (e: Exception) {
                AppLog.w("无法显示Toast，Context可能已销毁: $message", e)
            }
        }
    }

    // ========== 🎛️ 远程接收端设置控制对话框管理 ==========

    /**
     * 注册远程接收端设置控制对话框
     */
    fun registerReceiverSettingsControlDialog(receiverId: String, dialog: RemoteReceiverSettingsControlDialog) {
        activeReceiverSettingsControlDialogs[receiverId] = WeakReference(dialog)
        AppLog.d("注册远程接收端设置控制对话框: $receiverId")
    }

    /**
     * 注销远程接收端设置控制对话框
     */
    fun unregisterReceiverSettingsControlDialog(receiverId: String) {
        activeReceiverSettingsControlDialogs.remove(receiverId)
        AppLog.d("注销远程接收端设置控制对话框: $receiverId")
    }

    /**
     * 处理远程接收端设置同步消息
     */
    private fun handleRemoteReceiverSettingsSync(receiver: RemoteReceiverConnection, message: ControlMessage) {
        AppLog.d("【双向同步】处理远程接收端设置同步消息: ${receiver.deviceName}")

        // 获取设置数据
        val settings = message.data.toMutableMap()
        settings.remove("timestamp") // 移除时间戳，只保留设置数据

        AppLog.d("【双向同步】接收到设置同步数据: $settings")

        // 🔧 修复：使用通用的通知方法，不限制特定receiverId
        notifySettingsControlDialog(receiver.id, settings)
    }

    // ========== 🔄 双向同步：处理接收端到遥控端的反向同步消息 ==========

    /**
     * 处理接收端本地音视频服务状态变更
     */
    private fun handleReceiverLocalAudioVideoChanged(receiver: RemoteReceiverConnection, message: ControlMessage) {
        AppLog.d("【双向同步】处理接收端音视频服务状态变更: ${receiver.deviceName}")

        val enabled = message.getBooleanData("enabled") ?: return
        val source = message.getStringData("source") ?: "unknown"

        AppLog.d("【双向同步】音视频服务状态: $enabled，来源: $source")

        // 构造同步数据
        val settings = mapOf(
            "audio_video_enabled" to enabled,
            "source" to source,
            "action" to "local_audio_video_changed"
        )

        // 通知活跃的设置控制对话框
        notifySettingsControlDialog(receiver.id, settings)
    }

    /**
     * 处理接收端本地播放模式变更
     */
    private fun handleReceiverLocalPlaybackModeChanged(receiver: RemoteReceiverConnection, message: ControlMessage) {
        AppLog.d("【双向同步】处理接收端播放模式变更: ${receiver.deviceName}")

        val isSpeakerMode = message.getBooleanData("is_speaker_mode") ?: return
        val source = message.getStringData("source") ?: "unknown"

        AppLog.d("【双向同步】播放模式: ${if (isSpeakerMode) "扬声器" else "听筒"}，来源: $source")

        // 构造同步数据
        val settings = mapOf(
            "is_speaker_mode" to isSpeakerMode,
            "mode" to if (isSpeakerMode) "speaker" else "earpiece",
            "source" to source,
            "action" to "local_playback_mode_changed"
        )

        // 通知活跃的设置控制对话框
        notifySettingsControlDialog(receiver.id, settings)
    }

    /**
     * 处理接收端本地音量变更
     */
    private fun handleReceiverLocalVolumeChanged(receiver: RemoteReceiverConnection, message: ControlMessage) {
        AppLog.d("【双向同步】处理接收端音量变更: ${receiver.deviceName}")

        val volume = message.getIntData("volume") ?: return
        val source = message.getStringData("source") ?: "unknown"

        AppLog.d("【双向同步】音量: $volume%，来源: $source")

        // 构造同步数据
        val settings = mapOf(
            "volume" to volume,
            "source" to source,
            "action" to "local_volume_changed"
        )

        // 通知活跃的设置控制对话框
        notifySettingsControlDialog(receiver.id, settings)
    }

    /**
     * 处理接收端本地设置广播
     */
    private fun handleReceiverLocalSettingsBroadcast(receiver: RemoteReceiverConnection, message: ControlMessage) {
        AppLog.d("【双向同步】处理接收端设置广播: ${receiver.deviceName}")

        val settings = message.data.toMutableMap()
        settings.remove("timestamp") // 移除时间戳

        AppLog.d("【双向同步】设置广播数据: $settings")

        // 通知活跃的设置控制对话框
        notifySettingsControlDialog(receiver.id, settings)
    }

    /**
     * 通知设置控制对话框更新
     */
    private fun notifySettingsControlDialog(receiverId: String, settings: Map<String, Any>) {
        AppLog.d("【双向同步】准备通知设置控制对话框，receiverId: $receiverId，设置数据: $settings")
        AppLog.d("【双向同步】当前活跃的设置控制对话框数量: ${activeReceiverSettingsControlDialogs.size}")

        // 🔧 修复：尝试通知所有活跃的设置控制对话框，不限制特定receiverId
        var notified = false

        activeReceiverSettingsControlDialogs.entries.removeAll { (id, dialogRef) ->
            val dialog = dialogRef.get()
            if (dialog != null) {
                AppLog.d("【双向同步】通知设置控制对话框更新: $id，设置数据: $settings")
                dialog.onSettingsSyncReceived(settings)
                notified = true
                false // 保留有效的引用
            } else {
                AppLog.d("【双向同步】清理无效的设置控制对话框引用: $id")
                true // 移除无效的引用
            }
        }

        if (!notified) {
            AppLog.w("【双向同步】没有活跃的设置控制对话框需要通知，可能对话框未打开或已关闭")
        } else {
            AppLog.d("【双向同步】设置控制对话框通知完成")
        }
    }

    // ========== 🪟 投屏窗口管理功能 ==========

    // 活跃的远程窗口管理对话框引用
    private val activeWindowManagerDialogs = mutableMapOf<String, WeakReference<com.example.castapp.ui.dialog.RemoteWindowManagerDialog>>()

    // 🎯 全局的遥控端文字窗口管理器集合（按receiverId分组管理）
    private val globalActiveRemoteTextWindowManagers = mutableMapOf<String, MutableMap<String, com.example.castapp.ui.windowsettings.RemoteTextWindowManager>>()

    /**
     * 🪟 发送投屏窗口管理请求
     */
    fun sendWindowManagerRequest(receiver: RemoteReceiverConnection) {
        try {
            val client = connectionManager.getReceiverClient(receiver.id)
            if (client != null && client.getConnectionStatus()) {
                // 🔧 修复：使用WebSocket连接的实际连接ID，而不是receiver.id
                val actualConnectionId = client.getActualConnectionId()
                val requestMessage = ControlMessage.createWindowManagerRequest(actualConnectionId)
                client.sendMessage(requestMessage)
                AppLog.d("【窗口管理】发送窗口信息请求到: ${receiver.deviceName}")
                AppLog.d("【窗口管理】使用连接ID: $actualConnectionId (而非receiver.id: ${receiver.id})")
            } else {
                AppLog.w("【窗口管理】接收端未连接，无法发送窗口管理请求: ${receiver.deviceName}")
            }
        } catch (e: Exception) {
            AppLog.e("【窗口管理】发送窗口管理请求失败", e)
        }
    }

    /**
     * 🔄 发送窗口变换控制消息
     */
    fun sendWindowTransformControl(receiver: RemoteReceiverConnection, message: ControlMessage) {
        try {
            val client = connectionManager.getReceiverClient(receiver.id)
            if (client != null && client.getConnectionStatus()) {
                client.sendMessage(message)
                AppLog.d("【远程窗口同步】发送窗口变换控制消息到: ${receiver.deviceName}")
            } else {
                AppLog.w("【远程窗口同步】接收端未连接，无法发送窗口变换控制: ${receiver.deviceName}")
            }
        } catch (e: Exception) {
            AppLog.e("【远程窗口同步】发送窗口变换控制失败", e)
        }
    }

    /**
     * 🏷️ 发送备注更新消息
     */
    fun sendNoteUpdate(receiver: RemoteReceiverConnection, message: ControlMessage) {
        try {
            val client = connectionManager.getReceiverClient(receiver.id)
            if (client != null && client.getConnectionStatus()) {
                client.sendMessage(message)
                AppLog.d("【远程窗口同步】发送备注更新消息到: ${receiver.deviceName}")
            } else {
                AppLog.w("【远程窗口同步】接收端未连接，无法发送备注更新: ${receiver.deviceName}")
            }
        } catch (e: Exception) {
            AppLog.e("【远程窗口同步】发送备注更新失败", e)
        }
    }

    /**
     * 🪟 处理投屏窗口管理响应消息
     */
    private fun handleWindowManagerResponse(receiver: RemoteReceiverConnection, message: ControlMessage) {
        try {
            AppLog.d("【窗口管理】收到窗口管理响应: ${receiver.deviceName}")

            // 通知活跃的窗口管理对话框
            notifyWindowManagerDialogs(receiver.id, message)

        } catch (e: Exception) {
            AppLog.e("【窗口管理】处理窗口管理响应失败", e)
        }
    }

    /**
     * 🪟 注册远程窗口管理对话框
     */
    fun registerWindowManagerDialog(receiverId: String, dialog: com.example.castapp.ui.dialog.RemoteWindowManagerDialog) {
        activeWindowManagerDialogs[receiverId] = WeakReference(dialog)
        AppLog.d("【窗口管理】注册窗口管理对话框: $receiverId")
    }

    /**
     * 🪟 注销远程窗口管理对话框
     */
    fun unregisterWindowManagerDialog(receiverId: String) {
        activeWindowManagerDialogs.remove(receiverId)
        AppLog.d("【窗口管理】注销窗口管理对话框: $receiverId")
    }

    /**
     * 🪟 通知窗口管理对话框更新
     */
    private fun notifyWindowManagerDialogs(receiverId: String, message: ControlMessage) {
        val dialogRef = activeWindowManagerDialogs[receiverId]
        val dialog = dialogRef?.get()

        if (dialog != null) {
            AppLog.d("【窗口管理】通知窗口管理对话框更新: $receiverId")
            dialog.handleWindowManagerResponse(message)
        } else {
            // 清理无效的弱引用
            activeWindowManagerDialogs.remove(receiverId)
            AppLog.d("【窗口管理】清理无效的窗口管理对话框引用: $receiverId")
        }
    }

    // ========== 🎯 全局遥控端文字窗口管理器管理 ==========

    /**
     * 🎯 添加全局遥控端文字窗口管理器
     */
    fun addGlobalRemoteTextWindowManager(receiverId: String, connectionId: String, manager: com.example.castapp.ui.windowsettings.RemoteTextWindowManager) {
        synchronized(globalActiveRemoteTextWindowManagers) {
            val receiverManagers = globalActiveRemoteTextWindowManagers.getOrPut(receiverId) { mutableMapOf() }
            receiverManagers[connectionId] = manager
            AppLog.d("【全局遥控端文字窗口管理器】添加管理器: $receiverId -> $connectionId")
        }
    }

    /**
     * 🎯 获取全局遥控端文字窗口管理器
     */
    fun getGlobalRemoteTextWindowManager(receiverId: String, connectionId: String): com.example.castapp.ui.windowsettings.RemoteTextWindowManager? {
        return synchronized(globalActiveRemoteTextWindowManagers) {
            globalActiveRemoteTextWindowManagers[receiverId]?.get(connectionId)
        }
    }

    /**
     * 🎯 移除全局遥控端文字窗口管理器
     */
    fun removeGlobalRemoteTextWindowManager(receiverId: String, connectionId: String) {
        synchronized(globalActiveRemoteTextWindowManagers) {
            val receiverManagers = globalActiveRemoteTextWindowManagers[receiverId]
            if (receiverManagers != null) {
                receiverManagers.remove(connectionId)
                AppLog.d("【全局遥控端文字窗口管理器】移除管理器: $receiverId -> $connectionId")

                // 如果该接收端没有任何管理器了，清理整个接收端条目
                if (receiverManagers.isEmpty()) {
                    globalActiveRemoteTextWindowManagers.remove(receiverId)
                    AppLog.d("【全局遥控端文字窗口管理器】清理空的接收端条目: $receiverId")
                }
            }
        }
    }

    /**
     * 🎯 清理接收端的所有遥控端文字窗口管理器
     */
    fun clearGlobalRemoteTextWindowManagers(receiverId: String) {
        synchronized(globalActiveRemoteTextWindowManagers) {
            val receiverManagers = globalActiveRemoteTextWindowManagers.remove(receiverId)
            if (receiverManagers != null) {
                AppLog.d("【全局遥控端文字窗口管理器】清理接收端所有管理器: $receiverId, 数量: ${receiverManagers.size}")

                // 隐藏所有编辑面板
                receiverManagers.values.forEach { manager ->
                    try {
                        manager.hideEditPanel()
                    } catch (e: Exception) {
                        AppLog.e("【全局遥控端文字窗口管理器】清理管理器时隐藏编辑面板失败", e)
                    }
                }
            }
        }
    }

    // ========== 📸 截图功能 ==========

    /**
     * 📸 发送截图请求
     */
    fun sendScreenshotRequest(receiver: RemoteReceiverConnection) {
        try {
            val client = connectionManager.getReceiverClient(receiver.id)
            if (client != null && client.getConnectionStatus()) {
                // 🔧 修复：使用WebSocket连接的实际连接ID，而不是receiver.id
                val actualConnectionId = client.getActualConnectionId()
                val requestMessage = ControlMessage.createScreenshotRequest(actualConnectionId)
                client.sendMessage(requestMessage)
                AppLog.d("📸 发送截图请求到: ${receiver.deviceName}")
                AppLog.d("📸 使用连接ID: $actualConnectionId (而非receiver.id: ${receiver.id})")
            } else {
                AppLog.w("📸 接收端未连接，无法发送截图请求: ${receiver.deviceName}")
            }
        } catch (e: Exception) {
            AppLog.e("📸 发送截图请求失败", e)
        }
    }

    // ========== 📝 文字内容功能 ==========

    /**
     * 📝 发送文字内容请求
     */
    fun sendTextContentRequest(receiver: RemoteReceiverConnection) {
        try {
            val client = connectionManager.getReceiverClient(receiver.id)
            if (client != null && client.getConnectionStatus()) {
                // 🔧 修复：使用WebSocket连接的实际连接ID，而不是receiver.id
                val actualConnectionId = client.getActualConnectionId()
                val requestMessage = ControlMessage.createTextContentRequest(actualConnectionId)
                client.sendMessage(requestMessage)
                AppLog.d("📝 发送文字内容请求到: ${receiver.deviceName}")
                AppLog.d("📝 使用连接ID: $actualConnectionId (而非receiver.id: ${receiver.id})")
            } else {
                AppLog.w("📝 接收端未连接，无法发送文字内容请求: ${receiver.deviceName}")
            }
        } catch (e: Exception) {
            AppLog.e("📝 发送文字内容请求失败", e)
        }
    }

    /**
     * 📝 发送文字格式同步消息
     */
    fun sendTextFormatSyncMessage(receiver: RemoteReceiverConnection, formatData: Map<String, Any>) {
        try {
            val client = connectionManager.getReceiverClient(receiver.id)
            if (client != null && client.getConnectionStatus()) {
                val actualConnectionId = client.getActualConnectionId()
                val syncMessage = ControlMessage.createTextFormatSyncMessage(actualConnectionId, formatData)
                client.sendMessage(syncMessage)
                AppLog.d("📝 发送文字格式同步消息到: ${receiver.deviceName}")
                AppLog.d("📝 同步数据: connectionId=${formatData["connectionId"]}, 内容长度=${(formatData["textContent"] as? String)?.length ?: 0}")
            } else {
                AppLog.w("📝 接收端未连接，无法发送文字格式同步消息: ${receiver.deviceName}")
            }
        } catch (e: Exception) {
            AppLog.e("📝 发送文字格式同步消息失败", e)
        }
    }

    /**
     * 📝 检查接收端的同步开关状态
     */
    fun isSyncEnabledForReceiver(receiverId: String): Boolean {
        return try {
            // 获取对应的窗口管理对话框
            val dialogRef = activeWindowManagerDialogs[receiverId]
            val dialog = dialogRef?.get()

            if (dialog != null) {
                // 通过对话框获取同步开关状态
                val isEnabled = dialog.isSyncEnabled()
                AppLog.d("📝 接收端同步开关状态: $receiverId -> $isEnabled")
                isEnabled
            } else {
                AppLog.w("📝 未找到对应的窗口管理对话框: $receiverId")
                false
            }
        } catch (e: Exception) {
            AppLog.e("📝 检查同步开关状态失败: $receiverId", e)
            false
        }
    }

    /**
     * 📝 获取活跃的控制对话框
     */
    fun getActiveControlDialog(receiverId: String): com.example.castapp.ui.dialog.RemoteReceiverControlDialog? {
        return try {
            val dialogRef = activeReceiverControlDialogs[receiverId]
            val dialog = dialogRef?.get()

            if (dialog != null) {
                AppLog.d("📝 找到活跃的控制对话框: $receiverId")
                dialog
            } else {
                AppLog.w("📝 未找到活跃的控制对话框: $receiverId")
                null
            }
        } catch (e: Exception) {
            AppLog.e("📝 获取活跃控制对话框失败: $receiverId", e)
            null
        }
    }

    /**
     * 📸 处理截图响应消息
     */
    private fun handleScreenshotResponse(receiver: RemoteReceiverConnection, message: ControlMessage) {
        try {
            AppLog.d("📸 收到截图响应: ${receiver.deviceName}")

            // 通知活跃的远程控制对话框显示截图
            notifyReceiverControlDialogsScreenshot(receiver.id, message)

        } catch (e: Exception) {
            AppLog.e("📸 处理截图响应失败", e)
        }
    }

    /**
     * 📸 处理截图错误消息
     */
    private fun handleScreenshotError(receiver: RemoteReceiverConnection, message: ControlMessage) {
        try {
            val errorMessage = message.getStringData("error") ?: "未知错误"
            AppLog.e("📸 截图失败: ${receiver.deviceName}, 错误: $errorMessage")

            // 通知活跃的远程控制对话框显示错误
            notifyReceiverControlDialogsScreenshotError(receiver.id, errorMessage)

        } catch (e: Exception) {
            AppLog.e("📸 处理截图错误消息失败", e)
        }
    }

    /**
     * 📸 通知远程控制对话框显示截图
     */
    private fun notifyReceiverControlDialogsScreenshot(receiverId: String, message: ControlMessage) {
        val dialogRef = activeReceiverControlDialogs[receiverId]
        val dialog = dialogRef?.get()

        if (dialog != null) {
            AppLog.d("📸 通知远程控制对话框显示截图: $receiverId")
            dialog.handleScreenshotResponse(message)
        } else {
            // 清理无效的弱引用
            activeReceiverControlDialogs.remove(receiverId)
            AppLog.d("📸 清理无效的远程控制对话框引用: $receiverId")
        }
    }

    /**
     * 📸 通知远程控制对话框显示截图错误
     */
    private fun notifyReceiverControlDialogsScreenshotError(receiverId: String, errorMessage: String) {
        val dialogRef = activeReceiverControlDialogs[receiverId]
        val dialog = dialogRef?.get()

        if (dialog != null) {
            AppLog.d("📸 通知远程控制对话框显示截图错误: $receiverId")
            dialog.handleScreenshotError(errorMessage)
        } else {
            // 清理无效的弱引用
            activeReceiverControlDialogs.remove(receiverId)
            AppLog.d("📸 清理无效的远程控制对话框引用: $receiverId")
        }
    }

    // ========== 📝 文字内容功能处理方法 ==========

    /**
     * 📝 处理文字内容响应消息
     */
    private fun handleTextContentResponse(receiver: RemoteReceiverConnection, message: ControlMessage) {
        try {
            AppLog.d("📝 收到文字内容响应: ${receiver.deviceName}")

            // 通知活跃的远程控制对话框显示文字内容
            notifyReceiverControlDialogsTextContent(receiver.id, message)

        } catch (e: Exception) {
            AppLog.e("📝 处理文字内容响应失败", e)
        }
    }

    /**
     * 📝 处理文字内容错误消息
     */
    private fun handleTextContentError(receiver: RemoteReceiverConnection, message: ControlMessage) {
        try {
            val errorMessage = message.getStringData("error") ?: "未知错误"
            AppLog.e("📝 文字内容获取失败: ${receiver.deviceName}, 错误: $errorMessage")

            // 通知活跃的远程控制对话框显示错误
            notifyReceiverControlDialogsTextContentError(receiver.id, errorMessage)

        } catch (e: Exception) {
            AppLog.e("📝 处理文字内容错误失败", e)
        }
    }

    /**
     * 📝 通知远程控制对话框显示文字内容
     */
    private fun notifyReceiverControlDialogsTextContent(receiverId: String, message: ControlMessage) {
        val dialogRef = activeReceiverControlDialogs[receiverId]
        val dialog = dialogRef?.get()

        if (dialog != null) {
            AppLog.d("📝 通知远程控制对话框显示文字内容: $receiverId")
            dialog.handleTextContentResponse(message)
        } else {
            // 清理无效的弱引用
            activeReceiverControlDialogs.remove(receiverId)
            AppLog.d("📝 清理无效的远程控制对话框引用: $receiverId")
        }
    }

    /**
     * 📝 通知远程控制对话框显示文字内容错误
     */
    private fun notifyReceiverControlDialogsTextContentError(receiverId: String, errorMessage: String) {
        val dialogRef = activeReceiverControlDialogs[receiverId]
        val dialog = dialogRef?.get()

        if (dialog != null) {
            AppLog.d("📝 通知远程控制对话框显示文字内容错误: $receiverId")
            dialog.handleTextContentError(errorMessage)
        } else {
            // 清理无效的弱引用
            activeReceiverControlDialogs.remove(receiverId)
            AppLog.d("📝 清理无效的远程控制对话框引用: $receiverId")
        }
    }

    // ========== 📐 屏幕分辨率请求功能 ==========



    /**
     * 📐 获取缓存的屏幕分辨率信息
     */
    fun getCachedScreenResolution(receiverId: String): Pair<Int, Int>? {
        return cachedScreenResolutions[receiverId]
    }

    /**
     * 📐 处理屏幕分辨率响应消息
     */
    private fun handleScreenResolutionResponse(
        receiver: RemoteReceiverConnection,
        message: ControlMessage,
        onConnectionStateChanged: ((RemoteReceiverConnection, Boolean) -> Unit)?
    ) {
        val width = message.getIntData("width") ?: 0
        val height = message.getIntData("height") ?: 0

        AppLog.d("📐 收到屏幕分辨率响应: ${width}×${height} from ${receiver.deviceName}")

        if (width > 0 && height > 0) {
            // 📐 缓存分辨率信息
            cachedScreenResolutions[receiver.id] = Pair(width, height)
            AppLog.d("📐 已缓存屏幕分辨率: ${receiver.deviceName} -> ${width}×${height}")

            // 🔧 修复：获取实际的连接状态，而不是使用可能过时的receiver.isConnected
            val client = connectionManager.getReceiverClient(receiver.id)
            val actualConnectionState = client?.getConnectionStatus() ?: false

            AppLog.d("📐 检查实际连接状态: ${receiver.deviceName} -> $actualConnectionState")

            // 更新接收端屏幕分辨率信息和实际连接状态
            val updatedReceiver = receiver.withScreenResolution(width, height)
                .withConnectionState(actualConnectionState) // 使用实际连接状态

            // 更新状态
            onConnectionStateChanged?.invoke(updatedReceiver, actualConnectionState)

            AppLog.d("📐 已更新接收端屏幕分辨率: ${receiver.deviceName} -> ${width}×${height}, 连接状态: $actualConnectionState")
        } else {
            AppLog.w("📐 收到无效的屏幕分辨率响应: ${width}×${height}")
        }
    }

    // ========== 📱 摄像头窗口创建通知处理方法 ==========

    /**
     * 📱 处理摄像头窗口创建通知消息
     */
    private fun handleCameraWindowCreatedNotification(receiver: RemoteReceiverConnection, message: ControlMessage) {
        try {
            val cameraType = message.getStringData("cameraType") ?: return
            val displayName = message.getStringData("displayName") ?: return

            AppLog.d("📱 收到摄像头窗口创建通知: ${receiver.deviceName}, 类型: $cameraType, 名称: $displayName")

            // 通知活跃的远程控制对话框触发更新功能
            notifyReceiverControlDialogsTriggerUpdate(receiver.id)

        } catch (e: Exception) {
            AppLog.e("📱 处理摄像头窗口创建通知失败", e)
        }
    }

    /**
     * 📱 通知远程控制对话框触发更新功能
     */
    private fun notifyReceiverControlDialogsTriggerUpdate(receiverId: String) {
        val dialogRef = activeReceiverControlDialogs[receiverId]
        val dialog = dialogRef?.get()

        if (dialog != null) {
            AppLog.d("📱 通知远程控制对话框触发更新功能: $receiverId")

            // 在主线程中触发更新功能
            android.os.Handler(android.os.Looper.getMainLooper()).post {
                try {
                    // 通过反射调用handleUpdateClick方法
                    val updateMethod = dialog::class.java.getDeclaredMethod("handleUpdateClick")
                    updateMethod.isAccessible = true
                    updateMethod.invoke(dialog)

                    AppLog.d("📱 远程控制对话框更新功能已触发: $receiverId")
                } catch (e: Exception) {
                    AppLog.e("📱 触发远程控制对话框更新功能失败", e)
                }
            }
        } else {
            // 清理无效的弱引用
            activeReceiverControlDialogs.remove(receiverId)
            AppLog.d("📱 清理无效的远程控制对话框引用: $receiverId")
        }
    }


}
