package com.example.castapp.ui.dialog

import android.content.Context
import android.content.DialogInterface
import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.ImageView
import android.widget.LinearLayout
import android.widget.TextView
import android.widget.Toast
import androidx.appcompat.widget.SwitchCompat
import androidx.recyclerview.widget.LinearLayoutManager
import androidx.recyclerview.widget.RecyclerView
import com.google.android.material.bottomsheet.BottomSheetDialogFragment
import com.example.castapp.R
import com.example.castapp.model.CastWindowInfo
import com.example.castapp.ui.adapter.WindowManagerAdapter
import com.example.castapp.utils.AppLog
import com.example.castapp.websocket.ControlMessage
import com.example.castapp.model.RemoteReceiverConnection

/**
 * 🪟 远程投屏窗口管理BottomSheet对话框
 * 显示远程接收端的所有活跃投屏窗口信息（只读模式）
 */
class RemoteWindowManagerDialog(
    private val remoteReceiverConnection: RemoteReceiverConnection
) : BottomSheetDialogFragment() {

    // UI组件
    private lateinit var tvWindowSettingsTitle: TextView // 🎯 窗口设置标题
    private lateinit var tvWindowCount: TextView
    private lateinit var rvCastWindows: RecyclerView
    private lateinit var layoutEmptyState: LinearLayout
    private lateinit var switchSyncControl: SwitchCompat // 🔄 实时同步开关
    private lateinit var btnClose: ImageView // 🔄 关闭按钮
    private lateinit var adapter: WindowManagerAdapter

    // 窗口信息列表
    private var windowInfoList: List<CastWindowInfo> = emptyList()

    // 🔄 实时同步控制状态
    private var isSyncEnabled = false

    // 对话框关闭回调
    var onDialogDismissed: (() -> Unit)? = null

    // 🪟 窗口信息更新回调（用于同步可视化数据）
    var onWindowInfoUpdated: ((List<CastWindowInfo>) -> Unit)? = null

    // 🎯 裁剪模式控制回调
    var onCropModeControl: ((String, Boolean) -> Unit)? = null

    // 🎯 新增：裁剪状态查询回调
    var onCropStateQuery: (((String, Boolean) -> Unit) -> Unit)? = null

    // 🎯 新增：窗口参数更新回调（用于同步统一参数管理器）
    var onWindowParamsUpdated: ((String, CastWindowInfo) -> Unit)? = null

    // 🎯 统一配置管理器实例
    private val configManager = com.example.castapp.model.RemoteWindowConfigManager.getInstance()

    // 🎯 新增：控制是否触发可视化更新的标志
    private var shouldTriggerVisualizationUpdate = true

    companion object {
        fun newInstance(remoteReceiverConnection: RemoteReceiverConnection): RemoteWindowManagerDialog {
            return RemoteWindowManagerDialog(remoteReceiverConnection)
        }
    }

    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View? {
        return inflater.inflate(R.layout.dialog_window_settings, container, false)
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)

        initViews(view)
        setupRecyclerView()
        setupClickListeners()
        updateTitle()

        // 注册对话框到管理器
        registerDialog()

        // 🎯 窗口设置窗口始终使用本地缓存数据，不向接收端发送请求
        AppLog.d("【窗口设置】使用本地缓存数据，不向接收端发送请求")
        loadCachedWindowInfo()

        AppLog.d("【远程窗口管理】对话框已创建: ${remoteReceiverConnection.deviceName}")
    }

    /**
     * 初始化视图组件
     */
    private fun initViews(view: View) {
        tvWindowSettingsTitle = view.findViewById(R.id.tv_window_settings_title)
        tvWindowCount = view.findViewById(R.id.tv_window_count)
        rvCastWindows = view.findViewById(R.id.rv_cast_windows)
        layoutEmptyState = view.findViewById(R.id.layout_empty_state)
        switchSyncControl = view.findViewById(R.id.switch_sync_control)
        btnClose = view.findViewById(R.id.btn_close)

        // 🔄 先恢复保存的实时同步开关状态，再设置监听器
        restoreSyncControlState()
        setupSyncControlSwitch()

        // � 统一编辑状态管理：不再需要恢复遥控端编辑状态，统一使用WindowSettingsManager
    }

    /**
     * 🔄 恢复保存的实时同步开关状态
     */
    private fun restoreSyncControlState() {
        val sharedPrefs = requireContext().getSharedPreferences("remote_window_settings", Context.MODE_PRIVATE)
        val syncStateKey = "sync_enabled_${remoteReceiverConnection.id}"
        isSyncEnabled = sharedPrefs.getBoolean(syncStateKey, false)

        // 设置UI状态（不触发监听器）
        switchSyncControl.setOnCheckedChangeListener(null)
        switchSyncControl.isChecked = isSyncEnabled

        AppLog.d("【远程窗口实时同步】恢复实时同步开关状态: $isSyncEnabled (设备: ${remoteReceiverConnection.deviceName})")
    }

    /**
     * 🔄 保存实时同步开关状态
     */
    private fun saveSyncControlState() {
        val sharedPrefs = requireContext().getSharedPreferences("remote_window_settings", Context.MODE_PRIVATE)
        val syncStateKey = "sync_enabled_${remoteReceiverConnection.id}"
        sharedPrefs.edit().putBoolean(syncStateKey, isSyncEnabled).apply()

        AppLog.d("【远程窗口实时同步】保存实时同步开关状态: $isSyncEnabled (设备: ${remoteReceiverConnection.deviceName})")
    }

    /**
     * 🔄 设置实时同步开关监听器
     */
    private fun setupSyncControlSwitch() {
        switchSyncControl.setOnCheckedChangeListener { _, isChecked ->
            isSyncEnabled = isChecked
            updateAdapterSyncMode()

            // 🔧 保存状态到SharedPreferences
            saveSyncControlState()

            AppLog.d("【远程窗口实时同步】实时同步开关状态变更: $isChecked")

            if (isChecked) {
                Toast.makeText(requireContext(), "已启用窗口实时同步控制", Toast.LENGTH_SHORT).show()
            } else {
                Toast.makeText(requireContext(), "已禁用窗口实时同步控制", Toast.LENGTH_SHORT).show()
            }
        }
    }



    /**
     * 设置点击监听器
     */
    private fun setupClickListeners() {
        btnClose.setOnClickListener {
            AppLog.d("【远程窗口管理】用户点击关闭按钮")
            dismiss()
        }
    }

    /**
     * 🔄 更新适配器同步模式
     */
    private fun updateAdapterSyncMode() {
        if (::adapter.isInitialized) {
            AppLog.d("【实时同步】开始更新适配器同步模式: $isSyncEnabled")

            // 🔧 修复：控件始终保持启用状态，不受实时同步开关影响
            // 远程窗口管理场景下，控件应该始终可用，实时同步开关只控制是否发送WebSocket消息
            adapter.setRemoteMode(false)

            // 🎯 关键修复：编辑开关监听器始终设置，不受实时同步开关影响
            setupEditSwitchListener()

            // 如果实时同步开关开启，设置完整的窗口操作监听器
            if (isSyncEnabled) {
                AppLog.d("【实时同步】设置完整的窗口操作监听器")
                setupWindowOperationListeners()
            } else {
                AppLog.d("【实时同步】设置仅本地更新的监听器")
                // 🎯 关键修复：实时同步关闭时，设置仅本地更新的监听器
                setupLocalOnlyOperationListeners()
            }

            // 🔧 修复：只更新监听器设置，不重置UI状态
            AppLog.d("【实时同步】通知适配器更新监听器")
            adapter.updateListenersOnly()

            // 🎯 强制刷新：确保监听器立即生效
            adapter.notifyDataSetChanged()
            AppLog.d("【实时同步】适配器同步模式更新完成: $isSyncEnabled")
        }
    }

    /**
     * 设置RecyclerView
     */
    private fun setupRecyclerView() {
        adapter = WindowManagerAdapter()
        rvCastWindows.layoutManager = LinearLayoutManager(context)
        rvCastWindows.adapter = adapter

        // 🔧 修复：初始状态控件始终启用，实时同步开关只控制消息发送
        updateAdapterSyncMode()

        // 🎯 移除单独的裁剪开关监听器设置，统一通过updateAdapterSyncMode管理
        // setupCropSwitchListener()

        AppLog.d("【远程窗口管理】RecyclerView设置完成")
    }

    /**
     * 🎯 设置裁剪开关监听器
     */
    private fun setupCropSwitchListener() {
        adapter.setOnCropSwitchListener(object : WindowManagerAdapter.OnCropSwitchListener {
            override fun onCropSwitchChanged(connectionId: String, isEnabled: Boolean) {
                AppLog.d("【远程窗口管理】裁剪开关变化: $connectionId, 启用: $isEnabled")

                // 通知远程控制窗口进行裁剪模式控制
                onCropModeControl?.invoke(connectionId, isEnabled)

                // 🎯 更新本地缓存（无论实时同步开关状态如何）
                updateWindowInfoInCache(connectionId) { windowInfo ->
                    windowInfo.copy(isCropping = isEnabled)
                }

                AppLog.d("【裁剪开关】已更新本地缓存: $connectionId -> 裁剪${if (isEnabled) "开启" else "关闭"}")
            }
        })
    }

    /**
     * 更新标题显示
     */
    private fun updateTitle() {
        // 由于使用的是现有布局，标题已经在布局文件中设置
        // 这里可以通过其他方式显示远程设备信息，比如在窗口数量旁边显示
        AppLog.d("【远程窗口管理】标题更新完成，远程设备: ${remoteReceiverConnection.deviceName}")
    }

    /**
     * 🪟 处理接收到的窗口信息响应
     */
    fun handleWindowManagerResponse(message: ControlMessage) {
        try {
            val success = message.getBooleanData("success") ?: false

            if (success) {
                val windowCount = message.getIntData("window_count") ?: 0
                val windowInfoListData = message.data["window_info_list"] as? List<*>

                AppLog.d("【远程窗口管理】收到窗口信息响应，窗口数量: $windowCount")

                if (windowInfoListData != null) {
                    // 解析窗口信息列表
                    val parsedWindowList = parseWindowInfoList(windowInfoListData)
                    updateWindowList(parsedWindowList)
                } else {
                    showErrorState("窗口信息解析失败")
                }
            } else {
                val errorMessage = message.getStringData("error") ?: "未知错误"
                showErrorState("获取窗口信息失败: $errorMessage")
            }

        } catch (e: Exception) {
            AppLog.e("【远程窗口管理】处理窗口信息响应失败", e)
            showErrorState("数据处理失败: ${e.message}")
        }
    }

    /**
     * 解析窗口信息列表
     */
    private fun parseWindowInfoList(windowInfoListData: List<*>): List<CastWindowInfo> {
        return windowInfoListData.mapNotNull { item ->
            try {
                if (item is Map<*, *>) {
                    val windowData = item as Map<String, Any>

                    // 🪟 解析裁剪区域信息
                    val cropRectRatio = (windowData["cropRectRatio"] as? Map<*, *>)?.let { cropData ->
                        android.graphics.RectF(
                            (cropData["left"] as? Number)?.toFloat() ?: 0f,
                            (cropData["top"] as? Number)?.toFloat() ?: 0f,
                            (cropData["right"] as? Number)?.toFloat() ?: 1f,
                            (cropData["bottom"] as? Number)?.toFloat() ?: 1f
                        )
                    }

                    // 🪟 使用实际显示尺寸（考虑裁剪）
                    val actualDisplayWidth = (windowData["actualDisplayWidth"] as? Number)?.toInt() ?:
                                            (windowData["baseWindowWidth"] as? Number)?.toInt() ?: 0
                    val actualDisplayHeight = (windowData["actualDisplayHeight"] as? Number)?.toInt() ?:
                                             (windowData["baseWindowHeight"] as? Number)?.toInt() ?: 0

                    CastWindowInfo(
                        connectionId = windowData["connectionId"] as? String ?: "",
                        ipAddress = windowData["ipAddress"] as? String ?: "",
                        port = (windowData["port"] as? Number)?.toInt() ?: 0,
                        isActive = windowData["isActive"] as? Boolean ?: true,
                        deviceName = windowData["deviceName"] as? String,
                        note = windowData["note"] as? String, // 🏷️ 解析备注信息
                        positionX = (windowData["positionX"] as? Number)?.toFloat() ?: 0f,
                        positionY = (windowData["positionY"] as? Number)?.toFloat() ?: 0f,
                        scaleFactor = (windowData["scaleFactor"] as? Number)?.toFloat() ?: 1.0f,
                        rotationAngle = (windowData["rotationAngle"] as? Number)?.toFloat() ?: 0f,
                        zOrder = (windowData["zOrder"] as? Number)?.toInt() ?: 0,
                        isCropping = windowData["isCropping"] as? Boolean ?: false,
                        cropRectRatio = cropRectRatio,
                        isDragEnabled = windowData["isDragEnabled"] as? Boolean ?: false,
                        isScaleEnabled = windowData["isScaleEnabled"] as? Boolean ?: false,
                        isRotationEnabled = windowData["isRotationEnabled"] as? Boolean ?: false,
                        isVisible = windowData["isVisible"] as? Boolean ?: true,
                        isMirrored = windowData["isMirrored"] as? Boolean ?: false,
                        cornerRadius = (windowData["cornerRadius"] as? Number)?.toFloat() ?: 0f,
                        alpha = (windowData["alpha"] as? Number)?.toFloat() ?: 1.0f,
                        isControlEnabled = windowData["isControlEnabled"] as? Boolean ?: false,

                        // 🎯 添加边框参数解析
                        isBorderEnabled = windowData["isBorderEnabled"] as? Boolean ?: false,
                        borderColor = (windowData["borderColor"] as? Number)?.toInt() ?: android.graphics.Color.parseColor("#6B6B6B"),
                        borderWidth = (windowData["borderWidth"] as? Number)?.toFloat() ?: 2f,
                        // 🪟 使用实际显示尺寸而不是基础窗口尺寸
                        baseWindowWidth = actualDisplayWidth,
                        baseWindowHeight = actualDisplayHeight
                    )
                } else {
                    null
                }
            } catch (e: Exception) {
                AppLog.e("【远程窗口管理】解析单个窗口信息失败", e)
                null
            }
        }
    }

    /**
     * 更新窗口列表显示
     */
    private fun updateWindowList(windowList: List<CastWindowInfo>) {
        try {
            // � 统一编辑状态管理：对于文字窗口，使用WindowSettingsManager的编辑状态
            val mergedWindowList = windowList.map { windowInfo ->
                if (windowInfo.connectionId.startsWith("text_")) {
                    // 文字窗口：使用WindowSettingsManager的统一编辑状态
                    val windowSettingsManager = com.example.castapp.manager.WindowSettingsManager.getInstance()
                    val editState = windowSettingsManager.getEditState(windowInfo.connectionId)
                    windowInfo.copy(isEditEnabled = editState)
                } else {
                    // 非文字窗口：保持原状态
                    windowInfo
                }
            }

            this.windowInfoList = mergedWindowList

            AppLog.d("【远程窗口管理】更新窗口列表，数量: ${mergedWindowList.size}")
            mergedWindowList.forEachIndexed { index, windowInfo ->
                if (windowInfo.connectionId.startsWith("text_")) {
                    AppLog.d("【远程窗口管理】文字窗口 $index: ${windowInfo.getDisplayTextWithDevice()}, 遥控端编辑状态: ${windowInfo.isEditEnabled}")
                } else {
                    AppLog.d("【远程窗口管理】窗口 $index: ${windowInfo.getDisplayTextWithDevice()}")
                }
            }

            // 🎯 保存窗口信息到本地缓存
            val context = context
            if (context != null) {
                val cache = com.example.castapp.manager.RemoteWindowInfoCache.getInstance()
                cache.saveWindowInfo(context, remoteReceiverConnection.id, mergedWindowList)
            } else {
                AppLog.w("【远程窗口管理】Fragment未附加到Context，跳过缓存保存")
            }

            // 🪟 根据标志决定是否通知窗口信息更新回调（用于同步可视化数据）
            if (shouldTriggerVisualizationUpdate) {
                onWindowInfoUpdated?.invoke(mergedWindowList)
                AppLog.d("【远程窗口管理】已通知窗口信息更新回调")
            } else {
                AppLog.d("【远程窗口管理】跳过可视化更新回调，避免窗口闪烁")
            }

            activity?.runOnUiThread {
                // 更新窗口数量显示
                tvWindowCount.text = "${mergedWindowList.size}个窗口"

                // 提交新列表到适配器
                adapter.submitList(mergedWindowList.toList())

                // 显示/隐藏空状态
                if (mergedWindowList.isEmpty()) {
                    rvCastWindows.visibility = View.GONE
                    layoutEmptyState.visibility = View.VISIBLE
                    AppLog.d("【远程窗口管理】显示空状态")
                } else {
                    rvCastWindows.visibility = View.VISIBLE
                    layoutEmptyState.visibility = View.GONE
                    AppLog.d("【远程窗口管理】显示窗口列表")
                }
            } ?: run {
                // 如果没有activity上下文（比如临时处理器），只记录日志
                AppLog.d("【远程窗口管理】无activity上下文，跳过UI更新（临时处理器模式）")
            }

        } catch (e: Exception) {
            AppLog.e("【远程窗口管理】更新窗口列表失败", e)
        }
    }

    /**
     * 🎯 加载缓存的窗口信息
     */
    private fun loadCachedWindowInfo() {
        try {
            val context = context
            if (context == null) {
                AppLog.w("【获取接收端设置】Fragment未附加到Context，无法加载缓存")
                return
            }

            val cache = com.example.castapp.manager.RemoteWindowInfoCache.getInstance()
            val cachedWindowList = cache.loadWindowInfo(context, remoteReceiverConnection.id)

            if (cachedWindowList.isNotEmpty()) {
                AppLog.d("【获取接收端设置】从本地缓存加载窗口信息: ${cachedWindowList.size} 个窗口")

                // 🎯 关键修复：同步实际裁剪状态
                val syncedWindowList = syncRealTimeCropState(cachedWindowList)

                updateWindowList(syncedWindowList)
            } else {
                AppLog.d("【获取接收端设置】本地无缓存数据，显示空状态")
                showEmptyState("暂无缓存数据，请启用\"获取接收端设置\"开关获取最新信息")
            }
        } catch (e: Exception) {
            AppLog.e("【获取接收端设置】加载缓存窗口信息失败", e)
            showErrorState("加载缓存失败: ${e.message}")
        }
    }

    /**
     * 🎯 新增：同步实际裁剪状态
     * 检查当前是否有窗口处于裁剪模式，并更新缓存数据中的裁剪状态
     */
    private fun syncRealTimeCropState(cachedWindowList: List<com.example.castapp.model.CastWindowInfo>): List<com.example.castapp.model.CastWindowInfo> {
        return try {
            // 通过回调获取当前实际的裁剪状态
            val realTimeCropStates = mutableMapOf<String, Boolean>()

            // 请求获取实际裁剪状态
            onCropStateQuery?.invoke { connectionId, isCropping ->
                realTimeCropStates[connectionId] = isCropping
                AppLog.d("🎯 [实时裁剪状态] $connectionId -> $isCropping")
            }

            // 更新缓存数据中的裁剪状态
            cachedWindowList.map { windowInfo ->
                val realCropState = realTimeCropStates[windowInfo.connectionId]
                if (realCropState != null && realCropState != windowInfo.isCropping) {
                    AppLog.d("🎯 [裁剪状态同步] ${windowInfo.connectionId}: 缓存=${windowInfo.isCropping} -> 实际=$realCropState")
                    windowInfo.copy(isCropping = realCropState)
                } else {
                    windowInfo
                }
            }
        } catch (e: Exception) {
            AppLog.w("🎯 [裁剪状态同步] 同步实时裁剪状态失败: ${e.message}")
            cachedWindowList // 返回原始数据
        }
    }

    /**
     * 显示空状态
     */
    private fun showEmptyState(message: String = "暂无窗口") {
        activity?.runOnUiThread {
            tvWindowCount.text = "0个窗口"
            rvCastWindows.visibility = View.GONE
            layoutEmptyState.visibility = View.VISIBLE

            AppLog.d("【远程窗口管理】显示空状态: $message")
        }
    }

    /**
     * 🎯 更新本地缓存中的窗口信息
     */
    private fun updateWindowInfoInCache(connectionId: String, updateAction: (CastWindowInfo) -> CastWindowInfo) {
        try {
            val context = context
            if (context == null) {
                AppLog.w("【缓存更新】Fragment未附加到Context，无法更新缓存")
                return
            }

            // 更新内存中的窗口信息列表
            var updatedWindowInfo: CastWindowInfo? = null
            val updatedList = windowInfoList.map { windowInfo ->
                if (windowInfo.connectionId == connectionId) {
                    val updated = updateAction(windowInfo)
                    updatedWindowInfo = updated
                    updated
                } else {
                    windowInfo
                }
            }

            // 🎯 关键修复：同步更新内存中的列表
            windowInfoList = updatedList

            // 🎯 关键修复：立即更新适配器显示，确保UI组件获取到最新数据
            if (::adapter.isInitialized) {
                adapter.submitList(updatedList)
                AppLog.d("【缓存更新】已同步更新适配器数据: $connectionId")
            }

            // 🎯 使用统一配置管理器更新窗口配置
            updatedWindowInfo?.let { windowInfo ->
                configManager.updateWindowConfig(remoteReceiverConnection.id, connectionId) { _ ->
                    com.example.castapp.model.RemoteWindowConfig.fromCastWindowInfo(windowInfo).copy(
                        dataSource = "dialog_cache_update"
                    )
                }

                // 保存到本地存储
                configManager.saveToStorage(context, remoteReceiverConnection.id)

                // 🎯 调用参数更新回调，保持向后兼容
                onWindowParamsUpdated?.invoke(connectionId, windowInfo)
                AppLog.d("【参数同步】已通知统一配置管理器更新: $connectionId")
            }

            // 🎯 兼容性：同时保存到旧的缓存系统（后续可移除）
            val cache = com.example.castapp.manager.RemoteWindowInfoCache.getInstance()
            cache.saveWindowInfo(context, remoteReceiverConnection.id, updatedList)

            AppLog.d("【缓存更新】已更新统一配置管理器和缓存数据: $connectionId")

        } catch (e: Exception) {
            AppLog.e("【缓存更新】更新窗口信息缓存失败: $connectionId", e)
        }
    }

    /**
     * 显示错误状态
     */
    private fun showErrorState(errorMessage: String) {
        activity?.runOnUiThread {
            tvWindowCount.text = "获取失败"
            rvCastWindows.visibility = View.GONE
            layoutEmptyState.visibility = View.VISIBLE

            AppLog.d("【远程窗口管理】显示错误状态: $errorMessage")
        }
    }

    /**
     * 🔄 设置窗口操作监听器（实时同步开关开启时）
     */
    private fun setupWindowOperationListeners() {
        // 设置变换功能开关监听器
        adapter.setOnTransformSwitchListener(object : WindowManagerAdapter.OnTransformSwitchListener {
            override fun onDragSwitchChanged(connectionId: String, isEnabled: Boolean) {
                sendWindowTransformControl(connectionId, "drag_enabled", mapOf("enabled" to isEnabled))

                // 🎯 更新本地缓存
                updateWindowInfoInCache(connectionId) { windowInfo ->
                    windowInfo.copy(isDragEnabled = isEnabled)
                }
            }

            override fun onScaleSwitchChanged(connectionId: String, isEnabled: Boolean) {
                sendWindowTransformControl(connectionId, "scale_enabled", mapOf("enabled" to isEnabled))

                // 🎯 更新本地缓存
                updateWindowInfoInCache(connectionId) { windowInfo ->
                    windowInfo.copy(isScaleEnabled = isEnabled)
                }
            }

            override fun onRotationSwitchChanged(connectionId: String, isEnabled: Boolean) {
                sendWindowTransformControl(connectionId, "rotation_enabled", mapOf("enabled" to isEnabled))

                // 🎯 更新本地缓存
                updateWindowInfoInCache(connectionId) { windowInfo ->
                    windowInfo.copy(isRotationEnabled = isEnabled)
                }
            }
        })

        // 设置裁剪开关监听器
        adapter.setOnCropSwitchListener(object : WindowManagerAdapter.OnCropSwitchListener {
            override fun onCropSwitchChanged(connectionId: String, isEnabled: Boolean) {
                AppLog.d("【远程窗口管理】裁剪开关变化: $connectionId, 启用: $isEnabled")

                // 🎯 关键修复：首先控制遥控端进入/退出裁剪模式
                onCropModeControl?.invoke(connectionId, isEnabled)

                // 🎯 实时同步开启时，发送消息到接收端（在退出裁剪模式时发送参数）
                if (isSyncEnabled) {
                    AppLog.d("【实时同步】实时同步开启，将发送裁剪控制消息到接收端")
                    // 注意：实际的消息发送应该在退出裁剪模式时进行，这里只是标记
                    // sendWindowTransformControl(connectionId, "crop", mapOf("enabled" to isEnabled))
                }

                // 🎯 更新本地缓存
                updateWindowInfoInCache(connectionId) { windowInfo ->
                    windowInfo.copy(isCropping = isEnabled)
                }

                AppLog.d("【裁剪开关】已更新本地缓存: $connectionId -> 裁剪${if (isEnabled) "开启" else "关闭"}")
            }
        })

        // 设置可见性开关监听器
        adapter.setOnVisibilitySwitchListener(object : WindowManagerAdapter.OnVisibilitySwitchListener {
            override fun onVisibilitySwitchChanged(connectionId: String, isVisible: Boolean) {
                // 1. 发送消息到接收端
                sendWindowTransformControl(connectionId, "visibility", mapOf("visible" to isVisible))

                // 2. 🎯 更新遥控端本地窗口可见性
                updateLocalWindowVisibility(connectionId, isVisible)

                // 3. 🎯 更新本地缓存
                updateWindowInfoInCache(connectionId) { windowInfo ->
                    windowInfo.copy(isVisible = isVisible)
                }

                AppLog.d("【可见性开关】已同步更新接收端、本地窗口和缓存: $connectionId -> ${if (isVisible) "显示" else "隐藏"}")
            }
        })

        // 设置镜像开关监听器
        adapter.setOnMirrorSwitchListener(object : WindowManagerAdapter.OnMirrorSwitchListener {
            override fun onMirrorSwitchChanged(connectionId: String, isEnabled: Boolean) {
                val transformData = mapOf("enabled" to isEnabled)
                // 🎯 关键修复：同时更新接收端和遥控端本地可视化窗口
                sendWindowTransformControl(connectionId, "mirror", transformData)
                updateLocalVisualizationBorderParams(connectionId, "mirror", transformData)

                // 🎯 更新本地缓存
                updateWindowInfoInCache(connectionId) { windowInfo ->
                    windowInfo.copy(isMirrored = isEnabled)
                }

                AppLog.d("【遥控端镜像开关】已同步更新接收端、本地可视化窗口和缓存: $connectionId -> 镜像${if (isEnabled) "开启" else "关闭"}")
            }
        })

        // 设置圆角半径变化监听器
        adapter.setOnCornerRadiusChangeListener(object : WindowManagerAdapter.OnCornerRadiusChangeListener {
            override fun onCornerRadiusChanged(connectionId: String, cornerRadius: Float) {
                val transformData = mapOf("radius" to cornerRadius)
                // 🎯 关键修复：同时更新接收端和遥控端本地可视化窗口
                sendWindowTransformControl(connectionId, "corner_radius", transformData)
                updateLocalVisualizationBorderParams(connectionId, "corner_radius", transformData)

                // 🎯 更新本地缓存
                updateWindowInfoInCache(connectionId) { windowInfo ->
                    windowInfo.copy(cornerRadius = cornerRadius)
                }

                AppLog.d("【遥控端圆角半径】已同步更新接收端、本地可视化窗口和缓存: $connectionId -> ${cornerRadius}dp")
            }
        })

        // 设置透明度变化监听器
        adapter.setOnAlphaChangeListener(object : WindowManagerAdapter.OnAlphaChangeListener {
            override fun onAlphaChanged(connectionId: String, alpha: Float) {
                val transformData = mapOf("alpha" to alpha)
                // 🎯 关键修复：同时更新接收端和遥控端本地可视化窗口
                sendWindowTransformControl(connectionId, "alpha", transformData)
                updateLocalVisualizationBorderParams(connectionId, "alpha", transformData)

                // 🎯 更新本地缓存
                updateWindowInfoInCache(connectionId) { windowInfo ->
                    windowInfo.copy(alpha = alpha)
                }

                AppLog.d("【遥控端透明度】已同步更新接收端、本地可视化窗口和缓存: $connectionId -> ${(alpha * 100).toInt()}%")
            }
        })

        // 设置边框开关监听器
        adapter.setOnBorderSwitchListener(object : WindowManagerAdapter.OnBorderSwitchListener {
            override fun onBorderSwitchChanged(connectionId: String, isEnabled: Boolean) {
                val transformData = mapOf("enabled" to isEnabled)
                // 🎯 关键修复：同时更新接收端和遥控端本地可视化窗口
                sendWindowTransformControl(connectionId, "border", transformData)
                updateLocalVisualizationBorderParams(connectionId, "border", transformData)

                // 🎯 更新本地缓存
                updateWindowInfoInCache(connectionId) { windowInfo ->
                    windowInfo.copy(isBorderEnabled = isEnabled)
                }

                AppLog.d("【遥控端边框开关】已同步更新接收端、本地可视化窗口和缓存: $connectionId -> 边框${if (isEnabled) "开启" else "关闭"}")
            }
        })

        // 设置边框颜色变化监听器
        adapter.setOnBorderColorChangeListener(object : WindowManagerAdapter.OnBorderColorChangeListener {
            override fun onBorderColorChanged(connectionId: String, color: Int) {
                val transformData = mapOf("color" to color)
                // 🎯 关键修复：同时更新接收端和遥控端本地可视化窗口
                sendWindowTransformControl(connectionId, "border_color", transformData)
                updateLocalVisualizationBorderParams(connectionId, "border_color", transformData)

                // 🎯 更新本地缓存
                updateWindowInfoInCache(connectionId) { windowInfo ->
                    windowInfo.copy(borderColor = color)
                }

                AppLog.d("【遥控端边框颜色】已同步更新接收端、本地可视化窗口和缓存: $connectionId -> ${String.format("#%06X", 0xFFFFFF and color)}")
            }
        })

        // 设置边框宽度变化监听器
        adapter.setOnBorderWidthChangeListener(object : WindowManagerAdapter.OnBorderWidthChangeListener {
            override fun onBorderWidthChanged(connectionId: String, width: Float) {
                val transformData = mapOf("width" to width)
                // 🎯 关键修复：同时更新接收端和遥控端本地可视化窗口
                sendWindowTransformControl(connectionId, "border_width", transformData)
                updateLocalVisualizationBorderParams(connectionId, "border_width", transformData)

                // 🎯 更新本地缓存
                updateWindowInfoInCache(connectionId) { windowInfo ->
                    windowInfo.copy(borderWidth = width)
                }

                AppLog.d("【遥控端边框宽度】已同步更新接收端、本地可视化窗口和缓存: $connectionId -> ${width}dp")
            }
        })

        // 📝 设置编辑开关监听器（仅文字窗口）
        adapter.setOnEditSwitchListener(object : WindowManagerAdapter.OnEditSwitchListener {
            override fun onEditSwitchChanged(connectionId: String, isEnabled: Boolean) {
                handleTextWindowEditModeToggle(connectionId, isEnabled)
            }
        })

        // 🏷️ 设置备注变更监听器
        adapter.setOnNoteChangeListener(object : WindowManagerAdapter.OnNoteChangeListener {
            override fun onNoteChanged(connectionId: String, note: String) {
                sendNoteUpdate(connectionId, note)

                // 🎯 更新本地缓存
                updateWindowInfoInCache(connectionId) { windowInfo ->
                    windowInfo.copy(note = note)
                }

                AppLog.d("【备注更新】已同步更新接收端和本地缓存: $connectionId -> $note")
            }
        })

        // 🗑️ 设置窗口删除监听器
        adapter.setOnWindowDeleteListener(object : WindowManagerAdapter.OnWindowDeleteListener {
            override fun onWindowDelete(windowInfo: CastWindowInfo) {
                handleWindowDelete(windowInfo.connectionId)
            }
        })

        AppLog.d("【远程窗口同步】窗口操作监听器已设置")
    }

    /**
     * 🎯 设置仅本地更新的窗口操作监听器（实时同步开关关闭时）
     */
    private fun setupLocalOnlyOperationListeners() {
        // 设置变换功能开关监听器（仅本地更新）
        adapter.setOnTransformSwitchListener(object : WindowManagerAdapter.OnTransformSwitchListener {
            override fun onDragSwitchChanged(connectionId: String, isEnabled: Boolean) {
                // 🎯 仅本地：不发送到接收端，只更新缓存
                updateWindowInfoInCache(connectionId) { windowInfo ->
                    windowInfo.copy(isDragEnabled = isEnabled)
                }
            }

            override fun onScaleSwitchChanged(connectionId: String, isEnabled: Boolean) {
                // 🎯 仅本地：不发送到接收端，只更新缓存
                updateWindowInfoInCache(connectionId) { windowInfo ->
                    windowInfo.copy(isScaleEnabled = isEnabled)
                }
            }

            override fun onRotationSwitchChanged(connectionId: String, isEnabled: Boolean) {
                // 🎯 仅本地：不发送到接收端，只更新缓存
                updateWindowInfoInCache(connectionId) { windowInfo ->
                    windowInfo.copy(isRotationEnabled = isEnabled)
                }
            }
        })

        // 设置裁剪开关监听器（仅本地更新）
        adapter.setOnCropSwitchListener(object : WindowManagerAdapter.OnCropSwitchListener {
            override fun onCropSwitchChanged(connectionId: String, isEnabled: Boolean) {
                AppLog.d("【远程窗口管理】裁剪开关变化: $connectionId, 启用: $isEnabled")

                // 🎯 仅本地：控制遥控端进入/退出裁剪模式，不发送到接收端
                onCropModeControl?.invoke(connectionId, isEnabled)

                // 🎯 仅本地：更新本地缓存
                updateWindowInfoInCache(connectionId) { windowInfo ->
                    windowInfo.copy(isCropping = isEnabled)
                }

                AppLog.d("【裁剪开关】仅本地更新: $connectionId -> 裁剪${if (isEnabled) "开启" else "关闭"}")
            }
        })

        // 设置可见性开关监听器（仅本地更新）
        adapter.setOnVisibilitySwitchListener(object : WindowManagerAdapter.OnVisibilitySwitchListener {
            override fun onVisibilitySwitchChanged(connectionId: String, isVisible: Boolean) {
                // 🎯 仅本地：更新遥控端本地窗口可见性和缓存
                updateLocalWindowVisibility(connectionId, isVisible)
                updateWindowInfoInCache(connectionId) { windowInfo ->
                    windowInfo.copy(isVisible = isVisible)
                }
                AppLog.d("【可见性开关】仅本地更新: $connectionId -> ${if (isVisible) "显示" else "隐藏"}")
            }
        })

        // 设置镜像开关监听器（仅本地更新）
        adapter.setOnMirrorSwitchListener(object : WindowManagerAdapter.OnMirrorSwitchListener {
            override fun onMirrorSwitchChanged(connectionId: String, isEnabled: Boolean) {
                val transformData = mapOf("enabled" to isEnabled)
                // 🎯 仅本地：更新遥控端本地可视化窗口和缓存
                updateLocalVisualizationBorderParams(connectionId, "mirror", transformData)
                updateWindowInfoInCache(connectionId) { windowInfo ->
                    windowInfo.copy(isMirrored = isEnabled)
                }
                AppLog.d("【遥控端镜像开关】仅本地更新: $connectionId -> 镜像${if (isEnabled) "开启" else "关闭"}")
            }
        })

        // 设置圆角半径变化监听器（仅本地更新）
        adapter.setOnCornerRadiusChangeListener(object : WindowManagerAdapter.OnCornerRadiusChangeListener {
            override fun onCornerRadiusChanged(connectionId: String, cornerRadius: Float) {
                val transformData = mapOf("radius" to cornerRadius)
                // 🎯 仅本地：更新遥控端本地可视化窗口和缓存
                updateLocalVisualizationBorderParams(connectionId, "corner_radius", transformData)
                updateWindowInfoInCache(connectionId) { windowInfo ->
                    windowInfo.copy(cornerRadius = cornerRadius)
                }
                AppLog.d("【遥控端圆角半径】仅本地更新: $connectionId -> ${cornerRadius}dp")
            }
        })

        // 设置透明度变化监听器（仅本地更新）
        adapter.setOnAlphaChangeListener(object : WindowManagerAdapter.OnAlphaChangeListener {
            override fun onAlphaChanged(connectionId: String, alpha: Float) {
                val transformData = mapOf("alpha" to alpha)
                // 🎯 仅本地：更新遥控端本地可视化窗口和缓存
                updateLocalVisualizationBorderParams(connectionId, "alpha", transformData)
                updateWindowInfoInCache(connectionId) { windowInfo ->
                    windowInfo.copy(alpha = alpha)
                }
                AppLog.d("【遥控端透明度】仅本地更新: $connectionId -> ${(alpha * 100).toInt()}%")
            }
        })

        // 设置边框开关监听器（仅本地更新）
        adapter.setOnBorderSwitchListener(object : WindowManagerAdapter.OnBorderSwitchListener {
            override fun onBorderSwitchChanged(connectionId: String, isEnabled: Boolean) {
                val transformData = mapOf("enabled" to isEnabled)
                // 🎯 仅本地：更新遥控端本地可视化窗口和缓存
                updateLocalVisualizationBorderParams(connectionId, "border", transformData)
                updateWindowInfoInCache(connectionId) { windowInfo ->
                    windowInfo.copy(isBorderEnabled = isEnabled)
                }
                AppLog.d("【遥控端边框开关】仅本地更新: $connectionId -> 边框${if (isEnabled) "开启" else "关闭"}")
            }
        })

        // 设置边框颜色变化监听器（仅本地更新）
        adapter.setOnBorderColorChangeListener(object : WindowManagerAdapter.OnBorderColorChangeListener {
            override fun onBorderColorChanged(connectionId: String, color: Int) {
                val transformData = mapOf("color" to color)
                // 🎯 仅本地：更新遥控端本地可视化窗口和缓存
                updateLocalVisualizationBorderParams(connectionId, "border_color", transformData)
                updateWindowInfoInCache(connectionId) { windowInfo ->
                    windowInfo.copy(borderColor = color)
                }
                AppLog.d("【遥控端边框颜色】仅本地更新: $connectionId -> ${String.format("#%06X", 0xFFFFFF and color)}")
            }
        })

        // 设置边框宽度变化监听器（仅本地更新）
        adapter.setOnBorderWidthChangeListener(object : WindowManagerAdapter.OnBorderWidthChangeListener {
            override fun onBorderWidthChanged(connectionId: String, width: Float) {
                val transformData = mapOf("width" to width)
                // 🎯 仅本地：更新遥控端本地可视化窗口和缓存
                updateLocalVisualizationBorderParams(connectionId, "border_width", transformData)
                updateWindowInfoInCache(connectionId) { windowInfo ->
                    windowInfo.copy(borderWidth = width)
                }
                AppLog.d("【遥控端边框宽度】仅本地更新: $connectionId -> ${width}dp")
            }
        })

        // 🏷️ 设置备注变更监听器（仅本地更新）
        adapter.setOnNoteChangeListener(object : WindowManagerAdapter.OnNoteChangeListener {
            override fun onNoteChanged(connectionId: String, note: String) {
                // 🎯 仅本地：不发送到接收端，只更新缓存
                updateWindowInfoInCache(connectionId) { windowInfo ->
                    windowInfo.copy(note = note)
                }
                AppLog.d("【备注更新】仅本地更新: $connectionId -> $note")
            }
        })

        // 🗑️ 设置窗口删除监听器（仅本地更新）
        adapter.setOnWindowDeleteListener(object : WindowManagerAdapter.OnWindowDeleteListener {
            override fun onWindowDelete(windowInfo: CastWindowInfo) {
                handleWindowDeleteLocalOnly(windowInfo.connectionId)
            }
        })

        AppLog.d("【远程窗口管理】仅本地操作监听器已设置")
    }

    /**
     * 🎯 设置编辑开关监听器（独立于实时同步开关）
     */
    private fun setupEditSwitchListener() {
        if (::adapter.isInitialized) {
            // 📝 设置编辑开关监听器（仅文字窗口）
            adapter.setOnEditSwitchListener(object : WindowManagerAdapter.OnEditSwitchListener {
                override fun onEditSwitchChanged(connectionId: String, isEnabled: Boolean) {
                    handleTextWindowEditModeToggle(connectionId, isEnabled)
                }
            })
            AppLog.d("【远程窗口管理】编辑开关监听器已设置（独立于实时同步开关）")
        }
    }

    /**
     * 🔄 发送窗口变换控制消息
     */
    private fun sendWindowTransformControl(targetWindowId: String, transformType: String, transformData: Map<String, Any>) {
        try {
            // 🎥 根本解决方案：模拟摄像头容器ID映射到真实摄像头窗口ID
            val mappedTargetWindowId = when {
                targetWindowId == "front_camera_placeholder" -> {
                    AppLog.d("【远程窗口同步】🎥 模拟前置摄像头容器参数同步: $targetWindowId -> front_camera")
                    "front_camera"
                }
                targetWindowId == "rear_camera_placeholder" -> {
                    AppLog.d("【远程窗口同步】🎥 模拟后置摄像头容器参数同步: $targetWindowId -> rear_camera")
                    "rear_camera"
                }
                else -> {
                    targetWindowId // 普通窗口直接使用原ID
                }
            }

            val manager = com.example.castapp.manager.RemoteReceiverManager.getInstance()
            val message = ControlMessage.createRemoteWindowTransformControl(
                connectionId = remoteReceiverConnection.id,
                targetWindowId = mappedTargetWindowId,
                transformType = transformType,
                transformData = transformData
            )

            manager.sendWindowTransformControl(remoteReceiverConnection, message)

            AppLog.d("【远程窗口同步】发送窗口变换控制: $targetWindowId -> $mappedTargetWindowId -> $transformType")

        } catch (e: Exception) {
            AppLog.e("【远程窗口同步】发送窗口变换控制失败", e)
            Toast.makeText(requireContext(), "窗口控制失败: ${e.message}", Toast.LENGTH_SHORT).show()
        }
    }

    /**
     * �️ 发送备注更新消息
     */
    private fun sendNoteUpdate(targetWindowId: String, note: String) {
        try {
            // 🎥 根本解决方案：模拟摄像头容器ID映射到真实摄像头窗口ID
            val mappedTargetWindowId = when {
                targetWindowId == "front_camera_placeholder" -> {
                    AppLog.d("【远程窗口同步】🎥 模拟前置摄像头容器备注同步: $targetWindowId -> front_camera")
                    "front_camera"
                }
                targetWindowId == "rear_camera_placeholder" -> {
                    AppLog.d("【远程窗口同步】🎥 模拟后置摄像头容器备注同步: $targetWindowId -> rear_camera")
                    "rear_camera"
                }
                else -> {
                    targetWindowId // 普通窗口直接使用原ID
                }
            }

            val manager = com.example.castapp.manager.RemoteReceiverManager.getInstance()
            val message = ControlMessage.createNoteUpdate(
                connectionId = remoteReceiverConnection.id,
                targetWindowId = mappedTargetWindowId,
                note = note
            )

            manager.sendNoteUpdate(remoteReceiverConnection, message)
            AppLog.d("【远程窗口同步】发送备注更新: $targetWindowId -> $mappedTargetWindowId -> $note")

        } catch (e: Exception) {
            AppLog.e("【远程窗口同步】发送备注更新失败", e)
            Toast.makeText(requireContext(), "备注更新失败: ${e.message}", Toast.LENGTH_SHORT).show()
        }
    }

    /**
     * �🎯 新增：更新遥控端本地可视化窗口的边框参数
     */
    private fun updateLocalVisualizationBorderParams(targetWindowId: String, transformType: String, transformData: Map<String, Any>) {
        try {
            // 获取控制对话框的窗口可视化容器
            val controlDialog = com.example.castapp.manager.RemoteReceiverManager.getInstance()
                .getActiveControlDialog(remoteReceiverConnection.id)

            if (controlDialog == null) {
                AppLog.w("【遥控端边框更新】未找到活跃的控制对话框")
                return
            }

            val windowVisualizationView = controlDialog.getWindowVisualizationView()
            if (windowVisualizationView == null) {
                AppLog.w("【遥控端边框更新】未找到窗口可视化容器")
                return
            }

            // 获取当前可视化数据列表
            val currentVisualizationDataList = windowVisualizationView.getVisualizationDataList().toMutableList()

            // 查找目标窗口的可视化数据
            val targetDataIndex = currentVisualizationDataList.indexOfFirst { it.connectionId == targetWindowId }
            if (targetDataIndex == -1) {
                AppLog.w("【遥控端边框更新】未找到目标窗口的可视化数据: $targetWindowId")
                return
            }

            val targetData = currentVisualizationDataList[targetDataIndex]

            // 🎯 关键修复：对于文字窗口，从统一配置管理器中获取最新的文字内容，并更新当前尺寸
            val enhancedTargetData = if (targetWindowId.startsWith("text_")) {
                try {
                    val configManager = com.example.castapp.model.RemoteWindowConfigManager.getInstance()
                    val textConfig = configManager.getTextWindowConfig(remoteReceiverConnection.id, targetWindowId)

                    // 🎯 关键修复：获取当前容器的实际尺寸
                    val currentWidth = currentVisualizationDataList[targetDataIndex].let { data ->
                        // 查找对应的容器View获取当前尺寸
                        for (i in 0 until windowVisualizationView.childCount) {
                            val child = windowVisualizationView.getChildAt(i)
                            if (child is com.example.castapp.ui.view.WindowVisualizationContainerView) {
                                val childData = child.getWindowData()
                                if (childData?.connectionId == targetWindowId) {
                                    return@let child.width
                                }
                            }
                        }
                        data.originalWidth // 后备值
                    }

                    val currentHeight = currentVisualizationDataList[targetDataIndex].let { data ->
                        // 查找对应的容器View获取当前尺寸
                        for (i in 0 until windowVisualizationView.childCount) {
                            val child = windowVisualizationView.getChildAt(i)
                            if (child is com.example.castapp.ui.view.WindowVisualizationContainerView) {
                                val childData = child.getWindowData()
                                if (childData?.connectionId == targetWindowId) {
                                    return@let child.height
                                }
                            }
                        }
                        data.originalHeight // 后备值
                    }

                    if (textConfig != null) {
                        AppLog.d("【遥控端边框更新】📝 从统一配置管理器恢复文字内容: $targetWindowId, 内容='${textConfig.textContent}'")
                        AppLog.d("【遥控端边框更新】📏 更新当前尺寸: $targetWindowId, 尺寸=${currentWidth}x${currentHeight}")
                        targetData.copy(
                            textContent = textConfig.textContent,
                            richTextData = textConfig.richTextData,
                            isBold = textConfig.isBold,
                            isItalic = textConfig.isItalic,
                            fontSize = textConfig.fontSize,
                            fontName = textConfig.fontName,
                            fontFamily = textConfig.fontFamily,
                            lineSpacing = textConfig.lineSpacing,
                            textAlignment = textConfig.textAlignment,
                            isWindowColorEnabled = textConfig.isWindowTextColorEnabled,
                            windowBackgroundColor = textConfig.windowTextBackgroundColor,
                            // 🎯 关键修复：更新当前实际尺寸
                            originalWidth = currentWidth,
                            originalHeight = currentHeight,
                            visualizedWidth = currentWidth.toFloat(),
                            visualizedHeight = currentHeight.toFloat()
                        )
                    } else {
                        AppLog.w("【遥控端边框更新】📝 统一配置管理器中未找到文字窗口配置: $targetWindowId")
                        // 即使没有文字配置，也要更新尺寸
                        targetData.copy(
                            originalWidth = currentWidth,
                            originalHeight = currentHeight,
                            visualizedWidth = currentWidth.toFloat(),
                            visualizedHeight = currentHeight.toFloat()
                        )
                    }
                } catch (e: Exception) {
                    AppLog.e("【遥控端边框更新】📝 从统一配置管理器获取文字内容失败: $targetWindowId", e)
                    targetData
                }
            } else {
                targetData
            }

            // 根据变换类型更新对应的参数
            val updatedData = when (transformType) {
                "border" -> {
                    val enabled = transformData["enabled"] as? Boolean ?: return
                    enhancedTargetData.copy(isBorderEnabled = enabled)
                }
                "border_color" -> {
                    val color = (transformData["color"] as? Number)?.toInt() ?: return
                    enhancedTargetData.copy(borderColor = color)
                }
                "border_width" -> {
                    val width = (transformData["width"] as? Number)?.toFloat() ?: return
                    enhancedTargetData.copy(borderWidth = width)
                }
                "corner_radius" -> {
                    val radius = (transformData["radius"] as? Number)?.toFloat() ?: return
                    enhancedTargetData.copy(cornerRadius = radius)
                }
                "alpha" -> {
                    val alpha = (transformData["alpha"] as? Number)?.toFloat() ?: return
                    enhancedTargetData.copy(alpha = alpha)
                }
                "mirror" -> {
                    val enabled = transformData["enabled"] as? Boolean ?: return
                    enhancedTargetData.copy(isMirrored = enabled)
                }
                else -> {
                    AppLog.w("【遥控端边框更新】不支持的变换类型: $transformType")
                    return
                }
            }

            // 更新可视化数据列表
            currentVisualizationDataList[targetDataIndex] = updatedData

            // 在UI线程中更新可视化窗口
            activity?.runOnUiThread {
                windowVisualizationView.updateVisualizationData(currentVisualizationDataList)
                AppLog.d("【遥控端边框更新】本地可视化窗口已更新: $targetWindowId -> $transformType")
            }

        } catch (e: Exception) {
            AppLog.e("【遥控端边框更新】更新本地可视化窗口失败", e)
        }
    }

    /**
     * 📝 处理文字窗口编辑模式切换
     */
    private fun handleTextWindowEditModeToggle(connectionId: String, isEnabled: Boolean) {
        try {
            AppLog.d("【远程窗口管理】处理文字窗口编辑模式切换: $connectionId -> $isEnabled")

            // � 统一编辑状态管理：使用WindowSettingsManager保存编辑状态
            val windowSettingsManager = com.example.castapp.manager.WindowSettingsManager.getInstance()
            windowSettingsManager.setEditState(connectionId, isEnabled)

            if (isEnabled) {
                // 🎯 修复：开启编辑模式只在遥控端创建编辑界面，不发送消息到接收端
                createRemoteTextWindowForEditing(connectionId)
                AppLog.d("【远程窗口管理】遥控端文字窗口已进入编辑模式: $connectionId")
            } else {
                // 🎯 修复：关闭编辑模式时，隐藏编辑面板并根据实时同步开关决定是否同步
                hideRemoteTextWindowEditPanelWithSync(connectionId, isSyncEnabled)
            }

        } catch (e: Exception) {
            AppLog.e("【远程窗口管理】处理文字窗口编辑模式切换失败", e)
        }
    }

    /**
     * 📝 为编辑创建遥控端文字窗口
     */
    private fun createRemoteTextWindowForEditing(connectionId: String) {
        try {
            // 获取对应的文字窗口信息
            val windowInfo = windowInfoList.find { it.connectionId == connectionId }
            if (windowInfo == null) {
                AppLog.w("【远程窗口管理】未找到文字窗口信息: $connectionId")
                return
            }

            // 通过RemoteReceiverManager获取对应的RemoteReceiverControlDialog
            val receiverManager = com.example.castapp.manager.RemoteReceiverManager.getInstance()
            val controlDialog = receiverManager.getActiveControlDialog(remoteReceiverConnection.id)

            if (controlDialog == null) {
                AppLog.w("【远程窗口管理】未找到对应的远程控制对话框: ${remoteReceiverConnection.id}")
                return
            }

            // 获取控制对话框的窗口可视化容器
            val windowVisualizationView = controlDialog.getWindowVisualizationView()
            if (windowVisualizationView == null) {
                AppLog.w("【远程窗口管理】未找到窗口可视化容器")
                return
            }

            // 创建遥控端文字窗口管理器
            val remoteTextWindowManager = com.example.castapp.ui.windowsettings.RemoteTextWindowManager(
                context = requireContext(),
                textId = connectionId,
                initialTextContent = windowInfo.deviceName ?: "默认文字",
                remoteReceiverConnection = remoteReceiverConnection,
                windowVisualizationView = windowVisualizationView
            )

            // 🎯 设置同步回调函数，直接返回当前对话框的实时同步开关状态
            remoteTextWindowManager.setSyncCallback { isSyncEnabled }

            // 🎯 保存到全局管理器集合中
            receiverManager.addGlobalRemoteTextWindowManager(remoteReceiverConnection.id, connectionId, remoteTextWindowManager)

            // 显示编辑面板
            remoteTextWindowManager.showEditPanel()

            AppLog.d("【远程窗口管理】遥控端文字窗口编辑模式已启动: $connectionId")

        } catch (e: Exception) {
            AppLog.e("【远程窗口管理】创建遥控端文字窗口失败", e)
        }
    }

    /**
     * 📝 隐藏遥控端文字窗口编辑面板（带同步控制）
     */
    private fun hideRemoteTextWindowEditPanelWithSync(connectionId: String, shouldSync: Boolean) {
        try {
            AppLog.d("【远程窗口管理】隐藏遥控端文字窗口编辑面板: $connectionId, 是否同步: $shouldSync")

            // 🎯 修复：使用全局管理器来查找和隐藏编辑面板
            val receiverManager = com.example.castapp.manager.RemoteReceiverManager.getInstance()
            val remoteTextWindowManager = receiverManager.getGlobalRemoteTextWindowManager(remoteReceiverConnection.id, connectionId)

            if (remoteTextWindowManager != null) {
                // 隐藏编辑面板并根据参数决定是否同步
                remoteTextWindowManager.hideEditPanelWithSync(shouldSync)

                // 从全局管理器集合中移除
                receiverManager.removeGlobalRemoteTextWindowManager(remoteReceiverConnection.id, connectionId)

                if (shouldSync) {
                    AppLog.d("【远程窗口管理】遥控端文字窗口编辑面板已隐藏并同步到接收端: $connectionId")
                } else {
                    AppLog.d("【远程窗口管理】遥控端文字窗口编辑面板已隐藏，内容仅保存在遥控端: $connectionId")
                }
            } else {
                AppLog.w("【远程窗口管理】未找到对应的遥控端文字窗口管理器: $connectionId")
            }

        } catch (e: Exception) {
            AppLog.e("【远程窗口管理】隐藏编辑面板失败", e)
        }
    }

    /**
     * 📝 获取实时同步开关状态
     */
    fun isSyncEnabled(): Boolean {
        return isSyncEnabled
    }

    /**
     * 🪟 注册对话框到管理器
     */
    private fun registerDialog() {
        val manager = com.example.castapp.manager.RemoteReceiverManager.getInstance()
        manager.registerWindowManagerDialog(remoteReceiverConnection.id, this)
        AppLog.d("【远程窗口管理】对话框已注册: ${remoteReceiverConnection.id}")
    }

    /**
     * 🪟 注销对话框
     */
    private fun unregisterDialog() {
        val manager = com.example.castapp.manager.RemoteReceiverManager.getInstance()
        manager.unregisterWindowManagerDialog(remoteReceiverConnection.id)
        AppLog.d("【远程窗口管理】对话框已注销: ${remoteReceiverConnection.id}")
    }

    override fun onDismiss(dialog: DialogInterface) {
        super.onDismiss(dialog)

        // 注销对话框
        unregisterDialog()

        onDialogDismissed?.invoke()
        AppLog.d("【远程窗口管理】对话框已关闭")
    }

    /**
     * 🎯 更新遥控端本地可视化窗口的可见性
     */
    private fun updateLocalWindowVisibility(connectionId: String, isVisible: Boolean) {
        try {
            AppLog.d("【远程窗口管理】更新遥控端本地窗口可见性: $connectionId -> $isVisible")

            // 获取远程控制对话框的窗口可视化容器
            val manager = com.example.castapp.manager.RemoteReceiverManager.getInstance()
            val controlDialog = manager.getActiveControlDialog(remoteReceiverConnection.id)

            if (controlDialog == null) {
                AppLog.w("【远程窗口管理】未找到活跃的控制对话框")
                return
            }

            val windowVisualizationView = controlDialog.getWindowVisualizationView()
            if (windowVisualizationView == null) {
                AppLog.w("【远程窗口管理】未找到窗口可视化容器")
                return
            }

            // 查找对应的可视化窗口容器
            for (i in 0 until windowVisualizationView.childCount) {
                val child = windowVisualizationView.getChildAt(i)
                if (child is com.example.castapp.ui.view.WindowVisualizationContainerView) {
                    val windowData = child.getWindowData()
                    if (windowData?.connectionId == connectionId) {
                        val targetVisibility = if (isVisible) android.view.View.VISIBLE else android.view.View.GONE

                        // 1. 更新可视化窗口容器的可见性
                        child.visibility = targetVisibility

                        // 2. 🎯 同步更新边框视图的可见性
                        try {
                            val borderViewRefField = child.javaClass.getDeclaredField("borderViewRef")
                            borderViewRefField.isAccessible = true
                            val borderViewRef = borderViewRefField.get(child) as? java.lang.ref.WeakReference<*>
                            val borderView = borderViewRef?.get() as? android.view.View

                            if (borderView != null) {
                                borderView.visibility = targetVisibility
                                AppLog.d("【远程窗口管理】边框视图可见性已同步更新: $connectionId -> $isVisible")
                            } else {
                                AppLog.d("【远程窗口管理】未找到边框视图或边框视图已被回收: $connectionId")
                            }
                        } catch (e: Exception) {
                            AppLog.w("【远程窗口管理】更新边框视图可见性失败: $connectionId", e)
                        }

                        AppLog.d("【远程窗口管理】遥控端本地窗口和边框可见性已更新: $connectionId -> $isVisible")
                        return
                    }
                }
            }
            AppLog.w("【远程窗口管理】未找到对应的遥控端本地可视化窗口: $connectionId")

        } catch (e: Exception) {
            AppLog.e("【远程窗口管理】更新遥控端本地窗口可见性失败", e)
        }
    }

    /**
     * 🎯 设置是否触发可视化更新
     * @param shouldTrigger true=触发可视化更新（默认），false=不触发（避免闪烁）
     */
    fun setShouldTriggerVisualizationUpdate(shouldTrigger: Boolean) {
        shouldTriggerVisualizationUpdate = shouldTrigger
        AppLog.d("【远程窗口管理】设置可视化更新标志: $shouldTrigger")
    }

    /**
     * 🗑️ 处理窗口删除（实时同步开关开启时）
     */
    private fun handleWindowDelete(connectionId: String) {
        try {
            AppLog.d("【远程窗口删除】开始删除窗口: $connectionId, 实时同步: $isSyncEnabled")

            if (isSyncEnabled) {
                // 🎯 实时同步开启：发送删除指令到接收端
                sendWindowDeleteControl(connectionId)
                AppLog.d("【远程窗口删除】已发送删除指令到接收端: $connectionId")
            }

            // 🎯 删除遥控端本地可视化窗口
            removeLocalVisualizationWindow(connectionId)

            // 🎯 从本地缓存中移除窗口信息
            removeWindowFromCache(connectionId)

            AppLog.d("【远程窗口删除】窗口删除完成: $connectionId")

        } catch (e: Exception) {
            AppLog.e("【远程窗口删除】删除窗口失败: $connectionId", e)
            Toast.makeText(requireContext(), "删除窗口失败: ${e.message}", Toast.LENGTH_SHORT).show()
        }
    }

    /**
     * 🗑️ 处理窗口删除（仅本地，实时同步开关关闭时）
     */
    private fun handleWindowDeleteLocalOnly(connectionId: String) {
        try {
            AppLog.d("【远程窗口删除】仅本地删除窗口: $connectionId")

            // 🎯 删除遥控端本地可视化窗口
            removeLocalVisualizationWindow(connectionId)

            // 🎯 从本地缓存中移除窗口信息
            removeWindowFromCache(connectionId)

            AppLog.d("【远程窗口删除】仅本地删除完成: $connectionId")

        } catch (e: Exception) {
            AppLog.e("【远程窗口删除】仅本地删除失败: $connectionId", e)
            Toast.makeText(requireContext(), "删除窗口失败: ${e.message}", Toast.LENGTH_SHORT).show()
        }
    }

    /**
     * 🗑️ 发送窗口删除控制消息到接收端
     */
    private fun sendWindowDeleteControl(targetWindowId: String) {
        try {
            // 🎥 根本解决方案：模拟摄像头容器ID映射到真实摄像头窗口ID
            val mappedTargetWindowId = when {
                targetWindowId == "front_camera_placeholder" -> {
                    AppLog.d("【远程窗口删除】🎥 模拟前置摄像头容器删除: $targetWindowId -> front_camera")
                    "front_camera"
                }
                targetWindowId == "rear_camera_placeholder" -> {
                    AppLog.d("【远程窗口删除】🎥 模拟后置摄像头容器删除: $targetWindowId -> rear_camera")
                    "rear_camera"
                }
                else -> {
                    targetWindowId // 普通窗口直接使用原ID
                }
            }

            val manager = com.example.castapp.manager.RemoteReceiverManager.getInstance()
            val message = ControlMessage.createRemoteWindowTransformControl(
                connectionId = remoteReceiverConnection.id,
                targetWindowId = mappedTargetWindowId,
                transformType = "delete",
                transformData = mapOf("timestamp" to System.currentTimeMillis())
            )

            manager.sendWindowTransformControl(remoteReceiverConnection, message)
            AppLog.d("【远程窗口删除】发送删除控制消息: $targetWindowId -> $mappedTargetWindowId")

        } catch (e: Exception) {
            AppLog.e("【远程窗口删除】发送删除控制消息失败", e)
            throw e
        }
    }

    /**
     * 🗑️ 移除遥控端本地可视化窗口
     */
    private fun removeLocalVisualizationWindow(connectionId: String) {
        try {
            // 获取控制对话框的窗口可视化容器
            val controlDialog = com.example.castapp.manager.RemoteReceiverManager.getInstance()
                .getActiveControlDialog(remoteReceiverConnection.id)

            if (controlDialog == null) {
                AppLog.w("【远程窗口删除】未找到活跃的控制对话框")
                return
            }

            val windowVisualizationView = controlDialog.getWindowVisualizationView()
            if (windowVisualizationView == null) {
                AppLog.w("【远程窗口删除】未找到窗口可视化容器")
                return
            }

            // 🎯 修复：通过更新可视化数据来正确清理窗口，而不是直接removeView
            // 这样可以确保windowContainerViews映射表也被正确清理
            val currentVisualizationData = windowVisualizationView.getVisualizationDataList()
            val filteredData = currentVisualizationData.filter { it.connectionId != connectionId }

            if (filteredData.size < currentVisualizationData.size) {
                // 找到了要删除的窗口，更新可视化数据
                windowVisualizationView.updateVisualizationData(filteredData)
                AppLog.d("【远程窗口删除】已通过更新可视化数据移除遥控端本地窗口: $connectionId")
                AppLog.d("【远程窗口删除】窗口数量: ${currentVisualizationData.size} -> ${filteredData.size}")
            } else {
                AppLog.w("【远程窗口删除】未找到对应的遥控端本地可视化窗口: $connectionId")
            }

        } catch (e: Exception) {
            AppLog.e("【远程窗口删除】移除遥控端本地可视化窗口失败", e)
        }
    }

    /**
     * 🗑️ 从本地缓存中移除窗口信息
     */
    private fun removeWindowFromCache(connectionId: String) {
        try {
            val context = context
            if (context == null) {
                AppLog.w("【远程窗口删除】Fragment未附加到Context，无法更新缓存")
                return
            }

            // 从内存中的窗口信息列表中移除
            val updatedList = windowInfoList.filter { it.connectionId != connectionId }
            windowInfoList = updatedList

            // 立即更新适配器显示
            if (::adapter.isInitialized) {
                adapter.submitList(updatedList)
                AppLog.d("【远程窗口删除】已更新适配器数据，移除: $connectionId")
            }

            // 更新窗口数量显示
            activity?.runOnUiThread {
                tvWindowCount.text = "${updatedList.size}个窗口"

                // 显示/隐藏空状态
                if (updatedList.isEmpty()) {
                    rvCastWindows.visibility = View.GONE
                    layoutEmptyState.visibility = View.VISIBLE
                } else {
                    rvCastWindows.visibility = View.VISIBLE
                    layoutEmptyState.visibility = View.GONE
                }
            }

            // 🎯 从统一配置管理器中移除
            configManager.removeWindowConfig(remoteReceiverConnection.id, connectionId)
            configManager.saveToStorage(context, remoteReceiverConnection.id)

            // 🎯 兼容性：同时从旧的缓存系统中移除
            val cache = com.example.castapp.manager.RemoteWindowInfoCache.getInstance()
            cache.saveWindowInfo(context, remoteReceiverConnection.id, updatedList)

            AppLog.d("【远程窗口删除】已从缓存中移除窗口信息: $connectionId")

        } catch (e: Exception) {
            AppLog.e("【远程窗口删除】从缓存中移除窗口信息失败: $connectionId", e)
        }
    }
}
